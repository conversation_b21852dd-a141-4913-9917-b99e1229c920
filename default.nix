let
  pkgs = import (builtins.fetchGit {
    url = "**************:browserstack/bs-nixpkgs.git";
    rev = "9633d50256bb6f4e56b9d5d66636638a6c4b50db";
  }) {};

  config = {
    androidPlatform = "/usr/local/.browserstack/mobile";
    logsPath = "/var/log/browserstack";
    user = "ritesharora";
  };

  packages =
            pkgs.callPackage ./packages/appium {} ++
            pkgs.callPackage ./packages/build_tools {} ++
            pkgs.callPackage ./packages/dotenv {} ++
            pkgs.callPackage ./packages/build_tools {} ++
            pkgs.callPackage ./packages/supervise { inherit config; } ++
            pkgs.callPackage ./packages/vpn_reverse_tether {} ++
            pkgs.callPackage ./packages/privoxy {} ++
            pkgs.callPackage ./packages/deps {} ++
            pkgs.callPackage ./packages/env {} ++
            pkgs.callPackage ./packages/chromedriver {} ++
            pkgs.callPackage ./packages/device-packages.nix {} ++
            pkgs.callPackage ./packages/percy/percy_setup.nix {} ++
            pkgs.callPackage ./packages/gnirehtet {} ++
            pkgs.callPackage ./packages/vpn_config {};

in
  pkgs.bs-tools.broInstallerManifest packages
