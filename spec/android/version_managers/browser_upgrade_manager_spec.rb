require 'spec_helper'
require 'json'
require 'logger'
require 'android_toolkit'
require_relative '../../../android/version_managers/browser_upgrade_manager'

RSpec.describe BrowserUpgradeManager do
  let(:logger) { instance_double(Logger, info: nil, debug: nil, error: nil) }
  let(:device_id) { 'test_device_id' }
  let(:device_obj) do
    instance_double(BrowserStack::AndroidDevice, model: 'Pixel 5', os_version: '12', id: device_id,
                                                 dedicated_device?: false, manufacturer: 'Google')
  end
  let(:chrome_config) do
    JSON.parse(File.read(File.expand_path('../../../config/chrome_config.json', __dir__)))
  end
  let(:firefox_config) do
    JSON.parse(File.read(File.expand_path('../../../config/firefox_config.json', __dir__)))
  end

  before do
    allow(BrowserStack::AndroidDevice).to receive(:new).and_return(device_obj)
    adb_mock = instance_double(AndroidToolkit::ADB)
    allow(AndroidToolkit::ADB).to receive(:new).and_return(adb_mock)
    allow(adb_mock).to receive(:devices).and_return([])
    allow(adb_mock).to receive(:shell).and_return("135") # for chrome_version checks
  end

  describe '#get_browser_version_and_variant' do
    subject { described_class.new(device_id, logger, 'chrome') }

    before do
      allow(File).to receive(:read).with("config/chrome_config.json").and_return(chrome_config.to_json)
    end

    it 'returns the action of the matching rule' do
      expect(subject.get_browser_version_and_variant('chrome')).to include("version" => "135.0.7049.113")
    end

    it 'raises error if no rule matches' do
      allow(subject).to receive(:find_matching_rule).and_raise('No matching rule found')
      expect do
        subject.get_browser_version_and_variant('firefox')
      end.to raise_error(RuntimeError, /Error finding matching rule: No matching rule found/)
    end
  end

  describe '#find_matching_rule' do
    subject { described_class.new(device_id, logger, 'chrome') }

    before do
      allow(File).to receive(:read).with("config/chrome_config.json").and_return(chrome_config.to_json)
    end

    it 'returns the matching rule' do
      rule = subject.find_matching_rule('chrome')
      expect(rule["action"]["version"]).to eq("135.0.7049.113")
    end

    it 'raises error if no rule matches' do
      allow(subject).to receive(:rule_matches?).and_return(false)
      expect do
        subject.find_matching_rule('chrome')
      end.to raise_error(RuntimeError, /No matching rule found and no default rule configured/)
    end
  end

  describe '#rule_matches?' do
    subject { described_class.new(device_id, logger, 'chrome') }

    before do
      allow(File).to receive(:read).with("config/chrome_config.json").and_return(chrome_config.to_json)
    end

    let(:rule) { { "conditions" => { "os_version" => { "gte" => 12 } } } }

    it 'returns true when rule matches' do
      expect(subject.rule_matches?(rule)).to be true
    end

    it 'returns false when rule does not match' do
      allow(device_obj).to receive(:os_version).and_return('10')
      expect(subject.rule_matches?(rule)).to be false
    end
  end

  describe '#sort_browser_upgrade_rules' do
    subject { described_class.new(device_id, logger, 'chrome') }

    before do
      allow(File).to receive(:read).with("config/chrome_config.json").and_return(chrome_config.to_json)
    end

    it 'sorts rules with default last' do
      subject.sort_browser_upgrade_rules('chrome')
      sorted = subject.instance_variable_get(:@config)["chrome"]["rules"]
      expect(sorted.last["conditions"]["default"]).to be true
    end
  end

  describe '#handle_chrome_driver_path' do
    subject { described_class.new(device_id, logger, 'chrome') }

    before do
      allow(File).to receive(:read).with("config/chrome_config.json").and_return(chrome_config.to_json)
      stub_const("ExitFile", Class.new do
        def self.write(val)
          @val = val
        end

        class << self
          attr_reader :val
        end
      end)
    end

    it 'returns the chromedriver path for matching rule' do
      expect(subject.handle_chrome_driver_path).to eq("v135.0.7049.114/chromedriver")
    end

    it 'raises error if no rule matches' do
      allow(subject).to receive(:get_browser_version_and_variant).and_raise("No matching rule found")
      expect { subject.handle_chrome_driver_path }.to raise_error(/Error getting Chrome driver path/)
    end
  end

  context 'with firefox config' do
    subject { described_class.new(device_id, logger, 'firefox') }

    before do
      allow(File).to receive(:read).with("config/firefox_config.json").and_return(firefox_config.to_json)
    end

    it 'returns the default firefox action' do
      expect(subject.get_browser_version_and_variant('firefox')).to include("version" => "133.0")
    end
  end
end