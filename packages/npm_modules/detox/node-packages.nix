# This file has been generated by node2nix 1.11.1. Do not edit!

{nodeEnv, fetchurl, fetchgit, nix-gitignore, stdenv, lib, globalBuildInputs ? []}:

let
  sources = {
    "@colors/colors-1.6.0" = {
      name = "_at_colors_slash_colors";
      packageName = "@colors/colors";
      version = "1.6.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/@colors/colors/-/colors-1.6.0.tgz";
        sha512 = "Ir+AOibqzrIsL6ajt3Rz3LskB7OiMVHqltZmspbW/TJuTVuyOMirVqAkjfY6JISiLHgyNqicAC8AyHHGzNd/dA==";
      };
    };
    "@dabh/diagnostics-2.0.3" = {
      name = "_at_dabh_slash_diagnostics";
      packageName = "@dabh/diagnostics";
      version = "2.0.3";
      src = fetchurl {
        url = "https://registry.npmjs.org/@dabh/diagnostics/-/diagnostics-2.0.3.tgz";
        sha512 = "hrlQOIi7hAfzsMqlGSFyVucrx38O+j6wiGOf//H2ecvIEqYN4ADBSS2iLMh5UFyDunCNniUIPk/q3riFv45xRA==";
      };
    };
    "@flatten-js/interval-tree-1.1.3" = {
      name = "_at_flatten-js_slash_interval-tree";
      packageName = "@flatten-js/interval-tree";
      version = "1.1.3";
      src = fetchurl {
        url = "https://registry.npmjs.org/@flatten-js/interval-tree/-/interval-tree-1.1.3.tgz";
        sha512 = "xhFWUBoHJFF77cJO1D6REjdgJEMRf2Y2Z+eKEPav8evGKcLSnj1ud5pLXQSbGuxF3VSvT1rWhMfVpXEKJLTL+A==";
      };
    };
    "@types/triple-beam-1.3.5" = {
      name = "_at_types_slash_triple-beam";
      packageName = "@types/triple-beam";
      version = "1.3.5";
      src = fetchurl {
        url = "https://registry.npmjs.org/@types/triple-beam/-/triple-beam-1.3.5.tgz";
        sha512 = "6WaYesThRMCl19iryMYP7/x2OVgCtbIVflDGFpWnb9irXI3UjYE4AzmYuiUKY1AJstGijoY+MgUszMgRxIYTYw==";
      };
    };
    "@wix-pilot/core-3.3.2" = {
      name = "_at_wix-pilot_slash_core";
      packageName = "@wix-pilot/core";
      version = "3.3.2";
      src = fetchurl {
        url = "https://registry.npmjs.org/@wix-pilot/core/-/core-3.3.2.tgz";
        sha512 = "wRnsxD3218bf8aHZ6pyLm2Ob+jx5/S06Dx5Sm9ExO5yHAEjH+O3UfzPM3bzxTh2HT4KL3VpL9xL+5omSYqj1WA==";
      };
    };
    "@wix-pilot/detox-1.0.11" = {
      name = "_at_wix-pilot_slash_detox";
      packageName = "@wix-pilot/detox";
      version = "1.0.11";
      src = fetchurl {
        url = "https://registry.npmjs.org/@wix-pilot/detox/-/detox-1.0.11.tgz";
        sha512 = "oCeYz7EhcWOyp0KbaJ3hipMbZ+nWuZ7Pvjno3sUzP1EebrZBZoRoDp16JZMR6H+i+h8OdqkrviI0mR2mYFZzMg==";
      };
    };
    "ajv-8.17.1" = {
      name = "ajv";
      packageName = "ajv";
      version = "8.17.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/ajv/-/ajv-8.17.1.tgz";
        sha512 = "B/gBuNg5SiMTrPkC+A2+cW0RszwxYmn6VYxB/inlBStS5nx6xHIt/ehKRhIMhqusl7a8LjQoZnjCs5vhwxOQ1g==";
      };
    };
    "ansi-regex-5.0.1" = {
      name = "ansi-regex";
      packageName = "ansi-regex";
      version = "5.0.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz";
        sha512 = "quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==";
      };
    };
    "ansi-styles-4.3.0" = {
      name = "ansi-styles";
      packageName = "ansi-styles";
      version = "4.3.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz";
        sha512 = "zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==";
      };
    };
    "async-3.2.6" = {
      name = "async";
      packageName = "async";
      version = "3.2.6";
      src = fetchurl {
        url = "https://registry.npmjs.org/async/-/async-3.2.6.tgz";
        sha512 = "htCUDlxyyCLMgaM3xXg0C0LW2xqfuQ6p05pCEIsXuyQ+a1koYKTuBMzRNwmybfLgvJDMd0r1LTn4+E0Ti6C2AA==";
      };
    };
    "balanced-match-1.0.2" = {
      name = "balanced-match";
      packageName = "balanced-match";
      version = "1.0.2";
      src = fetchurl {
        url = "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz";
        sha512 = "3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==";
      };
    };
    "bluebird-3.7.2" = {
      name = "bluebird";
      packageName = "bluebird";
      version = "3.7.2";
      src = fetchurl {
        url = "https://registry.npmjs.org/bluebird/-/bluebird-3.7.2.tgz";
        sha512 = "XpNj6GDQzdfW+r2Wnn7xiSAd7TM3jzkxGXBGTtWKuSXv1xUV+azxAm8jdWZN06QTQk+2N2XB9jRDkvbmQmcRtg==";
      };
    };
    "brace-expansion-1.1.11" = {
      name = "brace-expansion";
      packageName = "brace-expansion";
      version = "1.1.11";
      src = fetchurl {
        url = "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz";
        sha512 = "iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==";
      };
    };
    "brace-expansion-2.0.1" = {
      name = "brace-expansion";
      packageName = "brace-expansion";
      version = "2.0.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.1.tgz";
        sha512 = "XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==";
      };
    };
    "browser-process-hrtime-1.0.0" = {
      name = "browser-process-hrtime";
      packageName = "browser-process-hrtime";
      version = "1.0.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/browser-process-hrtime/-/browser-process-hrtime-1.0.0.tgz";
        sha512 = "9o5UecI3GhkpM6DrXr69PblIuWxPKk9Y0jHBRhdocZ2y7YECBFCsHm79Pr3OyR2AvjhDkabFJaDJMYRazHgsow==";
      };
    };
    "bunyamin-1.6.3" = {
      name = "bunyamin";
      packageName = "bunyamin";
      version = "1.6.3";
      src = fetchurl {
        url = "https://registry.npmjs.org/bunyamin/-/bunyamin-1.6.3.tgz";
        sha512 = "m1hAijFhu8pFiidsVc0XEDic46uxPK+mKNLqkb5mluNx0nTolNzx/DjwMqHChQWCgfOLMjKYJJ2uPTQLE6t4Ng==";
      };
    };
    "bunyan-1.8.15" = {
      name = "bunyan";
      packageName = "bunyan";
      version = "1.8.15";
      src = fetchurl {
        url = "https://registry.npmjs.org/bunyan/-/bunyan-1.8.15.tgz";
        sha512 = "0tECWShh6wUysgucJcBAoYegf3JJoZWibxdqhTm7OHPeT42qdjkZ29QCMcKwbgU1kiH+auSIasNRXMLWXafXig==";
      };
    };
    "bunyan-2.0.5" = {
      name = "bunyan";
      packageName = "bunyan";
      version = "2.0.5";
      src = fetchurl {
        url = "https://registry.npmjs.org/bunyan/-/bunyan-2.0.5.tgz";
        sha512 = "Jvl74TdxCN6rSP9W1I6+UOUtwslTDqsSFkDqZlFb/ilaSvQ+bZAnXT/GT97IZ5L+Vph0joPZPhxUyn6FLNmFAA==";
      };
    };
    "bunyan-debug-stream-3.1.1" = {
      name = "bunyan-debug-stream";
      packageName = "bunyan-debug-stream";
      version = "3.1.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/bunyan-debug-stream/-/bunyan-debug-stream-3.1.1.tgz";
        sha512 = "LfMcz4yKM6s9BP5dfT63Prb5B2hAjReLAfQzLbNQF7qBHtn3P1v+/yn0SZ6UAr4PC3VZRX/QzK7HYkkY0ytokQ==";
      };
    };
    "caf-15.0.1" = {
      name = "caf";
      packageName = "caf";
      version = "15.0.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/caf/-/caf-15.0.1.tgz";
        sha512 = "Xp/IK6vMwujxWZXra7djdYzPdPnEQKa7Mudu2wZgDQ3TJry1I0TgtjEgwZHpoBcMp68j4fb0/FZ1SJyMEgJrXQ==";
      };
    };
    "camelcase-6.3.0" = {
      name = "camelcase";
      packageName = "camelcase";
      version = "6.3.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/camelcase/-/camelcase-6.3.0.tgz";
        sha512 = "Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA==";
      };
    };
    "chalk-4.1.2" = {
      name = "chalk";
      packageName = "chalk";
      version = "4.1.2";
      src = fetchurl {
        url = "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz";
        sha512 = "oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==";
      };
    };
    "cliui-8.0.1" = {
      name = "cliui";
      packageName = "cliui";
      version = "8.0.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/cliui/-/cliui-8.0.1.tgz";
        sha512 = "BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==";
      };
    };
    "color-3.2.1" = {
      name = "color";
      packageName = "color";
      version = "3.2.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/color/-/color-3.2.1.tgz";
        sha512 = "aBl7dZI9ENN6fUGC7mWpMTPNHmWUSNan9tuWN6ahh5ZLNk9baLJOnSMlrQkHcrfFgz2/RigjUVAjdx36VcemKA==";
      };
    };
    "color-convert-1.9.3" = {
      name = "color-convert";
      packageName = "color-convert";
      version = "1.9.3";
      src = fetchurl {
        url = "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz";
        sha512 = "QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==";
      };
    };
    "color-convert-2.0.1" = {
      name = "color-convert";
      packageName = "color-convert";
      version = "2.0.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz";
        sha512 = "RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==";
      };
    };
    "color-name-1.1.3" = {
      name = "color-name";
      packageName = "color-name";
      version = "1.1.3";
      src = fetchurl {
        url = "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz";
        sha512 = "72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==";
      };
    };
    "color-name-1.1.4" = {
      name = "color-name";
      packageName = "color-name";
      version = "1.1.4";
      src = fetchurl {
        url = "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz";
        sha512 = "dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==";
      };
    };
    "color-string-1.9.1" = {
      name = "color-string";
      packageName = "color-string";
      version = "1.9.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/color-string/-/color-string-1.9.1.tgz";
        sha512 = "shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==";
      };
    };
    "colorspace-1.1.4" = {
      name = "colorspace";
      packageName = "colorspace";
      version = "1.1.4";
      src = fetchurl {
        url = "https://registry.npmjs.org/colorspace/-/colorspace-1.1.4.tgz";
        sha512 = "BgvKJiuVu1igBUF2kEjRCZXol6wiiGbY5ipL/oVPwm0BL9sIpMIzM8IK7vwuxIIzOXMV3Ey5w+vxhm0rR/TN8w==";
      };
    };
    "concat-map-0.0.1" = {
      name = "concat-map";
      packageName = "concat-map";
      version = "0.0.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz";
        sha512 = "/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==";
      };
    };
    "core-util-is-1.0.3" = {
      name = "core-util-is";
      packageName = "core-util-is";
      version = "1.0.3";
      src = fetchurl {
        url = "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.3.tgz";
        sha512 = "ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==";
      };
    };
    "cross-spawn-7.0.6" = {
      name = "cross-spawn";
      packageName = "cross-spawn";
      version = "7.0.6";
      src = fetchurl {
        url = "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz";
        sha512 = "uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==";
      };
    };
    "decamelize-4.0.0" = {
      name = "decamelize";
      packageName = "decamelize";
      version = "4.0.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/decamelize/-/decamelize-4.0.0.tgz";
        sha512 = "9iE1PgSik9HeIIw2JO94IidnE3eBoQrFJ3w7sFuzSX4DpmZ3v5sZpUiV5Swcf6mQEF+Y0ru8Neo+p+nyh2J+hQ==";
      };
    };
    "dtrace-provider-0.8.8" = {
      name = "dtrace-provider";
      packageName = "dtrace-provider";
      version = "0.8.8";
      src = fetchurl {
        url = "https://registry.npmjs.org/dtrace-provider/-/dtrace-provider-0.8.8.tgz";
        sha512 = "b7Z7cNtHPhH9EJhNNbbeqTcXB8LGFFZhq1PGgEvpeHlzd36bhbdTWoE/Ba/YguqpBSlAPKnARWhVlhunCMwfxg==";
      };
    };
    "duplexer2-0.1.4" = {
      name = "duplexer2";
      packageName = "duplexer2";
      version = "0.1.4";
      src = fetchurl {
        url = "https://registry.npmjs.org/duplexer2/-/duplexer2-0.1.4.tgz";
        sha512 = "asLFVfWWtJ90ZyOUHMqk7/S2w2guQKxUI2itj3d92ADHhxUSbCMGi1f1cBcJ7xM1To+pE/Khbwo1yuNbMEPKeA==";
      };
    };
    "easy-stack-1.0.1" = {
      name = "easy-stack";
      packageName = "easy-stack";
      version = "1.0.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/easy-stack/-/easy-stack-1.0.1.tgz";
        sha512 = "wK2sCs4feiiJeFXn3zvY0p41mdU5VUgbgs1rNsc/y5ngFUijdWd+iIN8eoyuZHKB8xN6BL4PdWmzqFmxNg6V2w==";
      };
    };
    "emoji-regex-8.0.0" = {
      name = "emoji-regex";
      packageName = "emoji-regex";
      version = "8.0.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz";
        sha512 = "MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==";
      };
    };
    "enabled-2.0.0" = {
      name = "enabled";
      packageName = "enabled";
      version = "2.0.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/enabled/-/enabled-2.0.0.tgz";
        sha512 = "AKrN98kuwOzMIdAizXGI86UFBoo26CL21UM763y1h/GMSJ4/OHU9k2YlsmBpyScFo/wbLzWQJBMCW4+IO3/+OQ==";
      };
    };
    "escalade-3.2.0" = {
      name = "escalade";
      packageName = "escalade";
      version = "3.2.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz";
        sha512 = "WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==";
      };
    };
    "event-pubsub-4.3.0" = {
      name = "event-pubsub";
      packageName = "event-pubsub";
      version = "4.3.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/event-pubsub/-/event-pubsub-4.3.0.tgz";
        sha512 = "z7IyloorXvKbFx9Bpie2+vMJKKx1fH1EN5yiTfp8CiLOTptSYy1g8H4yDpGlEdshL1PBiFtBHepF2cNsqeEeFQ==";
      };
    };
    "execa-5.1.1" = {
      name = "execa";
      packageName = "execa";
      version = "5.1.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/execa/-/execa-5.1.1.tgz";
        sha512 = "8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==";
      };
    };
    "exeunt-1.1.0" = {
      name = "exeunt";
      packageName = "exeunt";
      version = "1.1.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/exeunt/-/exeunt-1.1.0.tgz";
        sha512 = "dd++Yn/0Fp+gtJ04YHov7MeAii+LFivJc6KqnJNfplzLVUkUDrfKoQDTLlCgzcW15vY5hKlHasWeIsQJ8agHsw==";
      };
    };
    "fast-deep-equal-3.1.3" = {
      name = "fast-deep-equal";
      packageName = "fast-deep-equal";
      version = "3.1.3";
      src = fetchurl {
        url = "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz";
        sha512 = "f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==";
      };
    };
    "fast-uri-3.0.6" = {
      name = "fast-uri";
      packageName = "fast-uri";
      version = "3.0.6";
      src = fetchurl {
        url = "https://registry.npmjs.org/fast-uri/-/fast-uri-3.0.6.tgz";
        sha512 = "Atfo14OibSv5wAp4VWNsFYE1AchQRTv9cBGWET4pZWHzYshFSS9NQI6I57rdKn9croWVMbYFbLhJ+yJvmZIIHw==";
      };
    };
    "fecha-4.2.3" = {
      name = "fecha";
      packageName = "fecha";
      version = "4.2.3";
      src = fetchurl {
        url = "https://registry.npmjs.org/fecha/-/fecha-4.2.3.tgz";
        sha512 = "OP2IUU6HeYKJi3i0z4A19kHMQoLVs4Hc+DPqqxI2h/DPZHTm/vjsfC6P0b4jCMy14XizLBqvndQ+UilD7707Jw==";
      };
    };
    "find-up-5.0.0" = {
      name = "find-up";
      packageName = "find-up";
      version = "5.0.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/find-up/-/find-up-5.0.0.tgz";
        sha512 = "78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==";
      };
    };
    "flat-5.0.2" = {
      name = "flat";
      packageName = "flat";
      version = "5.0.2";
      src = fetchurl {
        url = "https://registry.npmjs.org/flat/-/flat-5.0.2.tgz";
        sha512 = "b6suED+5/3rTpUBdG1gupIl8MPFCAMA0QXwmljLhvCUKcUvdE4gWky9zpuGCcXHOsz4J9wPGNWq6OKpmIzz3hQ==";
      };
    };
    "fn.name-1.1.0" = {
      name = "fn.name";
      packageName = "fn.name";
      version = "1.1.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/fn.name/-/fn.name-1.1.0.tgz";
        sha512 = "GRnmB5gPyJpAhTQdSZTSp9uaPSvl09KoYcMQtsB9rQoOmzs9dH6ffeccH+Z+cv6P68Hu5bC6JjRh4Ah/mHSNRw==";
      };
    };
    "fs-extra-11.3.0" = {
      name = "fs-extra";
      packageName = "fs-extra";
      version = "11.3.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/fs-extra/-/fs-extra-11.3.0.tgz";
        sha512 = "Z4XaCL6dUDHfP/jT25jJKMmtxvuwbkrD1vNSMFlo9lNLY2c5FHYSQgHPRZUjAB26TpDEoW9HCOgplrdbaPV/ew==";
      };
    };
    "fs.realpath-1.0.0" = {
      name = "fs.realpath";
      packageName = "fs.realpath";
      version = "1.0.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz";
        sha512 = "OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==";
      };
    };
    "funpermaproxy-1.1.0" = {
      name = "funpermaproxy";
      packageName = "funpermaproxy";
      version = "1.1.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/funpermaproxy/-/funpermaproxy-1.1.0.tgz";
        sha512 = "2Sp1hWuO8m5fqeFDusyhKqYPT+7rGLw34N3qonDcdRP8+n7M7Gl/yKp/q7oCxnnJ6pWCectOmLFJpsMU/++KrQ==";
      };
    };
    "get-caller-file-2.0.5" = {
      name = "get-caller-file";
      packageName = "get-caller-file";
      version = "2.0.5";
      src = fetchurl {
        url = "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz";
        sha512 = "DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==";
      };
    };
    "get-stream-6.0.1" = {
      name = "get-stream";
      packageName = "get-stream";
      version = "6.0.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/get-stream/-/get-stream-6.0.1.tgz";
        sha512 = "ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==";
      };
    };
    "glob-6.0.4" = {
      name = "glob";
      packageName = "glob";
      version = "6.0.4";
      src = fetchurl {
        url = "https://registry.npmjs.org/glob/-/glob-6.0.4.tgz";
        sha512 = "MKZeRNyYZAVVVG1oZeLaWie1uweH40m9AZwIwxyPbTSX4hHrVYSzLg0Ro5Z5R7XKkIX+Cc6oD1rqeDJnwsB8/A==";
      };
    };
    "glob-8.1.0" = {
      name = "glob";
      packageName = "glob";
      version = "8.1.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/glob/-/glob-8.1.0.tgz";
        sha512 = "r8hpEjiQEYlF2QU0df3dS+nxxSIreXQS1qRhMJM0Q5NDdR386C7jb7Hwwod8Fgiuex+k0GFjgft18yvxm5XoCQ==";
      };
    };
    "graceful-fs-4.2.11" = {
      name = "graceful-fs";
      packageName = "graceful-fs";
      version = "4.2.11";
      src = fetchurl {
        url = "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz";
        sha512 = "RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==";
      };
    };
    "has-flag-4.0.0" = {
      name = "has-flag";
      packageName = "has-flag";
      version = "4.0.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz";
        sha512 = "EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==";
      };
    };
    "human-signals-2.1.0" = {
      name = "human-signals";
      packageName = "human-signals";
      version = "2.1.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/human-signals/-/human-signals-2.1.0.tgz";
        sha512 = "B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==";
      };
    };
    "inflight-1.0.6" = {
      name = "inflight";
      packageName = "inflight";
      version = "1.0.6";
      src = fetchurl {
        url = "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz";
        sha512 = "k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==";
      };
    };
    "inherits-2.0.4" = {
      name = "inherits";
      packageName = "inherits";
      version = "2.0.4";
      src = fetchurl {
        url = "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz";
        sha512 = "k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==";
      };
    };
    "ini-1.3.8" = {
      name = "ini";
      packageName = "ini";
      version = "1.3.8";
      src = fetchurl {
        url = "https://registry.npmjs.org/ini/-/ini-1.3.8.tgz";
        sha512 = "JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew==";
      };
    };
    "is-arrayish-0.3.2" = {
      name = "is-arrayish";
      packageName = "is-arrayish";
      version = "0.3.2";
      src = fetchurl {
        url = "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.3.2.tgz";
        sha512 = "eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==";
      };
    };
    "is-fullwidth-code-point-3.0.0" = {
      name = "is-fullwidth-code-point";
      packageName = "is-fullwidth-code-point";
      version = "3.0.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz";
        sha512 = "zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==";
      };
    };
    "is-plain-obj-2.1.0" = {
      name = "is-plain-obj";
      packageName = "is-plain-obj";
      version = "2.1.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/is-plain-obj/-/is-plain-obj-2.1.0.tgz";
        sha512 = "YWnfyRwxL/+SsrWYfOpUtz5b3YD+nyfkHvjbcanzk8zgyO4ASD67uVMRt8k5bM4lLMDnXfriRhOpemw+NfT1eA==";
      };
    };
    "is-stream-2.0.1" = {
      name = "is-stream";
      packageName = "is-stream";
      version = "2.0.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/is-stream/-/is-stream-2.0.1.tgz";
        sha512 = "hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==";
      };
    };
    "isarray-1.0.0" = {
      name = "isarray";
      packageName = "isarray";
      version = "1.0.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz";
        sha512 = "VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==";
      };
    };
    "isexe-2.0.0" = {
      name = "isexe";
      packageName = "isexe";
      version = "2.0.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz";
        sha512 = "RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==";
      };
    };
    "jest-environment-emit-1.0.8" = {
      name = "jest-environment-emit";
      packageName = "jest-environment-emit";
      version = "1.0.8";
      src = fetchurl {
        url = "https://registry.npmjs.org/jest-environment-emit/-/jest-environment-emit-1.0.8.tgz";
        sha512 = "WNqvxBLH0yNojHJQ99Y21963aT7UTavxV3PgiBQFi8zwrlnKU6HvkB6LOvQrbk5I8mI8JEKvcoOrQOvBVMLIXQ==";
      };
    };
    "js-message-1.0.7" = {
      name = "js-message";
      packageName = "js-message";
      version = "1.0.7";
      src = fetchurl {
        url = "https://registry.npmjs.org/js-message/-/js-message-1.0.7.tgz";
        sha512 = "efJLHhLjIyKRewNS9EGZ4UpI8NguuL6fKkhRxVuMmrGV2xN/0APGdQYwLFky5w9naebSZ0OwAGp0G6/2Cg90rA==";
      };
    };
    "js-queue-2.0.2" = {
      name = "js-queue";
      packageName = "js-queue";
      version = "2.0.2";
      src = fetchurl {
        url = "https://registry.npmjs.org/js-queue/-/js-queue-2.0.2.tgz";
        sha512 = "pbKLsbCfi7kriM3s1J4DDCo7jQkI58zPLHi0heXPzPlj0hjUsm+FesPUbE0DSbIVIK503A36aUBoCN7eMFedkA==";
      };
    };
    "json-cycle-1.5.0" = {
      name = "json-cycle";
      packageName = "json-cycle";
      version = "1.5.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/json-cycle/-/json-cycle-1.5.0.tgz";
        sha512 = "GOehvd5PO2FeZ5T4c+RxobeT5a1PiGpF4u9/3+UvrMU4bhnVqzJY7hm39wg8PDCqkU91fWGH8qjWR4bn+wgq9w==";
      };
    };
    "json-schema-traverse-1.0.0" = {
      name = "json-schema-traverse";
      packageName = "json-schema-traverse";
      version = "1.0.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz";
        sha512 = "NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==";
      };
    };
    "jsonfile-6.1.0" = {
      name = "jsonfile";
      packageName = "jsonfile";
      version = "6.1.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/jsonfile/-/jsonfile-6.1.0.tgz";
        sha512 = "5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==";
      };
    };
    "kuler-2.0.0" = {
      name = "kuler";
      packageName = "kuler";
      version = "2.0.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/kuler/-/kuler-2.0.0.tgz";
        sha512 = "Xq9nH7KlWZmXAtodXDDRE7vs6DU1gTU8zYDHDiWLSip45Egwq3plLHzPn27NgvzL2r1LMPC1vdqh98sQxtqj4A==";
      };
    };
    "locate-path-6.0.0" = {
      name = "locate-path";
      packageName = "locate-path";
      version = "6.0.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/locate-path/-/locate-path-6.0.0.tgz";
        sha512 = "iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==";
      };
    };
    "lodash-4.17.21" = {
      name = "lodash";
      packageName = "lodash";
      version = "4.17.21";
      src = fetchurl {
        url = "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz";
        sha512 = "v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==";
      };
    };
    "lodash.merge-4.6.2" = {
      name = "lodash.merge";
      packageName = "lodash.merge";
      version = "4.6.2";
      src = fetchurl {
        url = "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz";
        sha512 = "0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==";
      };
    };
    "logform-2.7.0" = {
      name = "logform";
      packageName = "logform";
      version = "2.7.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/logform/-/logform-2.7.0.tgz";
        sha512 = "TFYA4jnP7PVbmlBIfhlSe+WKxs9dklXMTEGcBCIvLhE/Tn3H6Gk1norupVW7m5Cnd4bLcr08AytbyV/xj7f/kQ==";
      };
    };
    "merge-stream-2.0.0" = {
      name = "merge-stream";
      packageName = "merge-stream";
      version = "2.0.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz";
        sha512 = "abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==";
      };
    };
    "mimic-fn-2.1.0" = {
      name = "mimic-fn";
      packageName = "mimic-fn";
      version = "2.1.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.1.0.tgz";
        sha512 = "OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==";
      };
    };
    "minimatch-3.1.2" = {
      name = "minimatch";
      packageName = "minimatch";
      version = "3.1.2";
      src = fetchurl {
        url = "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz";
        sha512 = "J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==";
      };
    };
    "minimatch-5.1.6" = {
      name = "minimatch";
      packageName = "minimatch";
      version = "5.1.6";
      src = fetchurl {
        url = "https://registry.npmjs.org/minimatch/-/minimatch-5.1.6.tgz";
        sha512 = "lKwV/1brpG6mBUFHtb7NUmtABCb2WZZmm2wNiOA5hAb8VdCS4B3dtMWyvcoViccwAW/COERjXLt0zP1zXUN26g==";
      };
    };
    "minimist-1.2.8" = {
      name = "minimist";
      packageName = "minimist";
      version = "1.2.8";
      src = fetchurl {
        url = "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz";
        sha512 = "2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==";
      };
    };
    "mkdirp-0.5.6" = {
      name = "mkdirp";
      packageName = "mkdirp";
      version = "0.5.6";
      src = fetchurl {
        url = "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.6.tgz";
        sha512 = "FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==";
      };
    };
    "moment-2.30.1" = {
      name = "moment";
      packageName = "moment";
      version = "2.30.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/moment/-/moment-2.30.1.tgz";
        sha512 = "uEmtNhbDOrWPFS+hdjFCBfy9f2YoyzRpwcl+DqpC6taX21FzsTLQVbMV/W7PzNSX6x/bhC1zA3c2UQ5NzH6how==";
      };
    };
    "ms-2.1.3" = {
      name = "ms";
      packageName = "ms";
      version = "2.1.3";
      src = fetchurl {
        url = "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz";
        sha512 = "6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==";
      };
    };
    "multi-sort-stream-1.0.4" = {
      name = "multi-sort-stream";
      packageName = "multi-sort-stream";
      version = "1.0.4";
      src = fetchurl {
        url = "https://registry.npmjs.org/multi-sort-stream/-/multi-sort-stream-1.0.4.tgz";
        sha512 = "hAZ8JOEQFbgdLe8HWZbb7gdZg0/yAIHF00Qfo3kd0rXFv96nXe+/bPTrKHZ2QMHugGX4FiAyET1Lt+jiB+7Qlg==";
      };
    };
    "multipipe-4.0.0" = {
      name = "multipipe";
      packageName = "multipipe";
      version = "4.0.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/multipipe/-/multipipe-4.0.0.tgz";
        sha512 = "jzcEAzFXoWwWwUbvHCNPwBlTz3WCWe/jPcXSmTfbo/VjRwRTfvLZ/bdvtiTdqCe8d4otCSsPCbhGYcX+eggpKQ==";
      };
    };
    "mv-2.1.1" = {
      name = "mv";
      packageName = "mv";
      version = "2.1.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/mv/-/mv-2.1.1.tgz";
        sha512 = "at/ZndSy3xEGJ8i0ygALh8ru9qy7gWW1cmkaqBN29JmMlIvM//MEO9y1sk/avxuwnPcfhkejkLsuPxH81BrkSg==";
      };
    };
    "nan-2.22.2" = {
      name = "nan";
      packageName = "nan";
      version = "2.22.2";
      src = fetchurl {
        url = "https://registry.npmjs.org/nan/-/nan-2.22.2.tgz";
        sha512 = "DANghxFkS1plDdRsX0X9pm0Z6SJNN6gBdtXfanwoZ8hooC5gosGFSBGRYHUVPz1asKA/kMRqDRdHrluZ61SpBQ==";
      };
    };
    "ncp-2.0.0" = {
      name = "ncp";
      packageName = "ncp";
      version = "2.0.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/ncp/-/ncp-2.0.0.tgz";
        sha512 = "zIdGUrPRFTUELUvr3Gmc7KZ2Sw/h1PiVM0Af/oHB6zgnV1ikqSfRk+TOufi79aHYCW3NiOXmr1BP5nWbzojLaA==";
      };
    };
    "node-ipc-9.2.1" = {
      name = "node-ipc";
      packageName = "node-ipc";
      version = "9.2.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/node-ipc/-/node-ipc-9.2.1.tgz";
        sha512 = "mJzaM6O3xHf9VT8BULvJSbdVbmHUKRNOH7zDDkCrA1/T+CVjq2WVIDfLt0azZRXpgArJtl3rtmEozrbXPZ9GaQ==";
      };
    };
    "npm-run-path-4.0.1" = {
      name = "npm-run-path";
      packageName = "npm-run-path";
      version = "4.0.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/npm-run-path/-/npm-run-path-4.0.1.tgz";
        sha512 = "S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==";
      };
    };
    "object-assign-4.1.1" = {
      name = "object-assign";
      packageName = "object-assign";
      version = "4.1.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz";
        sha512 = "rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==";
      };
    };
    "once-1.4.0" = {
      name = "once";
      packageName = "once";
      version = "1.4.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/once/-/once-1.4.0.tgz";
        sha512 = "lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==";
      };
    };
    "one-time-1.0.0" = {
      name = "one-time";
      packageName = "one-time";
      version = "1.0.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/one-time/-/one-time-1.0.0.tgz";
        sha512 = "5DXOiRKwuSEcQ/l0kGCF6Q3jcADFv5tSmRaJck/OqkVFcOzutB134KRSfF0xDrL39MNnqxbHBbUUcjZIhTgb2g==";
      };
    };
    "onetime-5.1.2" = {
      name = "onetime";
      packageName = "onetime";
      version = "5.1.2";
      src = fetchurl {
        url = "https://registry.npmjs.org/onetime/-/onetime-5.1.2.tgz";
        sha512 = "kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==";
      };
    };
    "p-limit-3.1.0" = {
      name = "p-limit";
      packageName = "p-limit";
      version = "3.1.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz";
        sha512 = "TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==";
      };
    };
    "p-locate-5.0.0" = {
      name = "p-locate";
      packageName = "p-locate";
      version = "5.0.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/p-locate/-/p-locate-5.0.0.tgz";
        sha512 = "LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==";
      };
    };
    "path-exists-4.0.0" = {
      name = "path-exists";
      packageName = "path-exists";
      version = "4.0.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz";
        sha512 = "ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==";
      };
    };
    "path-is-absolute-1.0.1" = {
      name = "path-is-absolute";
      packageName = "path-is-absolute";
      version = "1.0.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz";
        sha512 = "AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==";
      };
    };
    "path-key-3.1.1" = {
      name = "path-key";
      packageName = "path-key";
      version = "3.1.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz";
        sha512 = "ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==";
      };
    };
    "pngjs-7.0.0" = {
      name = "pngjs";
      packageName = "pngjs";
      version = "7.0.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/pngjs/-/pngjs-7.0.0.tgz";
        sha512 = "LKWqWJRhstyYo9pGvgor/ivk2w94eSjE3RGVuzLGlr3NmD8bf7RcYGze1mNdEHRP6TRP6rMuDHk5t44hnTRyow==";
      };
    };
    "process-nextick-args-2.0.1" = {
      name = "process-nextick-args";
      packageName = "process-nextick-args";
      version = "2.0.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.1.tgz";
        sha512 = "3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==";
      };
    };
    "promisify-child-process-4.1.2" = {
      name = "promisify-child-process";
      packageName = "promisify-child-process";
      version = "4.1.2";
      src = fetchurl {
        url = "https://registry.npmjs.org/promisify-child-process/-/promisify-child-process-4.1.2.tgz";
        sha512 = "APnkIgmaHNJpkAn7k+CrJSi9WMuff5ctYFbD0CO2XIPkM8yO7d/ShouU2clywbpHV/DUsyc4bpJCsNgddNtx4g==";
      };
    };
    "proper-lockfile-3.2.0" = {
      name = "proper-lockfile";
      packageName = "proper-lockfile";
      version = "3.2.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/proper-lockfile/-/proper-lockfile-3.2.0.tgz";
        sha512 = "iMghHHXv2bsxl6NchhEaFck8tvX3F9cknEEh1SUpguUOBjN7PAAW9BLzmbc1g/mCD1gY3EE2EABBHPJfFdHFmA==";
      };
    };
    "readable-stream-2.3.8" = {
      name = "readable-stream";
      packageName = "readable-stream";
      version = "2.3.8";
      src = fetchurl {
        url = "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.8.tgz";
        sha512 = "8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==";
      };
    };
    "readable-stream-3.6.2" = {
      name = "readable-stream";
      packageName = "readable-stream";
      version = "3.6.2";
      src = fetchurl {
        url = "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz";
        sha512 = "9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==";
      };
    };
    "require-directory-2.1.1" = {
      name = "require-directory";
      packageName = "require-directory";
      version = "2.1.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz";
        sha512 = "fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==";
      };
    };
    "require-from-string-2.0.2" = {
      name = "require-from-string";
      packageName = "require-from-string";
      version = "2.0.2";
      src = fetchurl {
        url = "https://registry.npmjs.org/require-from-string/-/require-from-string-2.0.2.tgz";
        sha512 = "Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==";
      };
    };
    "resolve-from-5.0.0" = {
      name = "resolve-from";
      packageName = "resolve-from";
      version = "5.0.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/resolve-from/-/resolve-from-5.0.0.tgz";
        sha512 = "qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==";
      };
    };
    "retry-0.12.0" = {
      name = "retry";
      packageName = "retry";
      version = "0.12.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/retry/-/retry-0.12.0.tgz";
        sha512 = "9LkiTwjUh6rT555DtE9rTX+BKByPfrMzEAtnlEtdEwr3Nkffwiihqe2bWADg+OQRjt9gl6ICdmB/ZFDCGAtSow==";
      };
    };
    "rimraf-2.4.5" = {
      name = "rimraf";
      packageName = "rimraf";
      version = "2.4.5";
      src = fetchurl {
        url = "https://registry.npmjs.org/rimraf/-/rimraf-2.4.5.tgz";
        sha512 = "J5xnxTyqaiw06JjMftq7L9ouA448dw/E7dKghkP9WpKNuwmARNNg+Gk8/u5ryb9N/Yo2+z3MCwuqFK/+qPOPfQ==";
      };
    };
    "safe-buffer-5.1.2" = {
      name = "safe-buffer";
      packageName = "safe-buffer";
      version = "5.1.2";
      src = fetchurl {
        url = "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz";
        sha512 = "Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==";
      };
    };
    "safe-json-stringify-1.2.0" = {
      name = "safe-json-stringify";
      packageName = "safe-json-stringify";
      version = "1.2.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/safe-json-stringify/-/safe-json-stringify-1.2.0.tgz";
        sha512 = "gH8eh2nZudPQO6TytOvbxnuhYBOvDBBLW52tz5q6X58lJcd/tkmqFR+5Z9adS8aJtURSXWThWy/xJtJwixErvg==";
      };
    };
    "safe-stable-stringify-2.5.0" = {
      name = "safe-stable-stringify";
      packageName = "safe-stable-stringify";
      version = "2.5.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/safe-stable-stringify/-/safe-stable-stringify-2.5.0.tgz";
        sha512 = "b3rppTKm9T+PsVCBEOUR46GWI7fdOs00VKZ1+9c1EWDaDMvjQc6tUwuFyIprgGgTcWoVHSKrU8H31ZHA2e0RHA==";
      };
    };
    "sanitize-filename-1.6.3" = {
      name = "sanitize-filename";
      packageName = "sanitize-filename";
      version = "1.6.3";
      src = fetchurl {
        url = "https://registry.npmjs.org/sanitize-filename/-/sanitize-filename-1.6.3.tgz";
        sha512 = "y/52Mcy7aw3gRm7IrcGDFx/bCk4AhRh2eI9luHOQM86nZsqwiRkkq2GekHXBBD+SmPidc8i2PqtYZl+pWJ8Oeg==";
      };
    };
    "semver-7.7.2" = {
      name = "semver";
      packageName = "semver";
      version = "7.7.2";
      src = fetchurl {
        url = "https://registry.npmjs.org/semver/-/semver-7.7.2.tgz";
        sha512 = "RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==";
      };
    };
    "serialize-error-8.1.0" = {
      name = "serialize-error";
      packageName = "serialize-error";
      version = "8.1.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/serialize-error/-/serialize-error-8.1.0.tgz";
        sha512 = "3NnuWfM6vBYoy5gZFvHiYsVbafvI9vZv/+jlIigFn4oP4zjNPK3LhcY0xSCgeb1a5L8jO71Mit9LlNoi2UfDDQ==";
      };
    };
    "shebang-command-2.0.0" = {
      name = "shebang-command";
      packageName = "shebang-command";
      version = "2.0.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz";
        sha512 = "kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==";
      };
    };
    "shebang-regex-3.0.0" = {
      name = "shebang-regex";
      packageName = "shebang-regex";
      version = "3.0.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz";
        sha512 = "7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==";
      };
    };
    "shell-quote-1.8.3" = {
      name = "shell-quote";
      packageName = "shell-quote";
      version = "1.8.3";
      src = fetchurl {
        url = "https://registry.npmjs.org/shell-quote/-/shell-quote-1.8.3.tgz";
        sha512 = "ObmnIF4hXNg1BqhnHmgbDETF8dLPCggZWBjkQfhZpbszZnYur5DUljTcCHii5LC3J5E0yeO/1LIMyH+UvHQgyw==";
      };
    };
    "signal-exit-3.0.7" = {
      name = "signal-exit";
      packageName = "signal-exit";
      version = "3.0.7";
      src = fetchurl {
        url = "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz";
        sha512 = "wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==";
      };
    };
    "simple-swizzle-0.2.2" = {
      name = "simple-swizzle";
      packageName = "simple-swizzle";
      version = "0.2.2";
      src = fetchurl {
        url = "https://registry.npmjs.org/simple-swizzle/-/simple-swizzle-0.2.2.tgz";
        sha512 = "JA//kQgZtbuY83m+xT+tXJkmJncGMTFT+C+g2h2R9uxkYIrE2yy9sgmcLhCnw57/WSD+Eh3J97FPEDFnbXnDUg==";
      };
    };
    "stack-trace-0.0.10" = {
      name = "stack-trace";
      packageName = "stack-trace";
      version = "0.0.10";
      src = fetchurl {
        url = "https://registry.npmjs.org/stack-trace/-/stack-trace-0.0.10.tgz";
        sha512 = "KGzahc7puUKkzyMt+IqAep+TVNbKP+k2Lmwhub39m1AsTSkaDutx56aDCo+HLDzf/D26BIHTJWNiTG1KAJiQCg==";
      };
    };
    "stream-chain-2.2.5" = {
      name = "stream-chain";
      packageName = "stream-chain";
      version = "2.2.5";
      src = fetchurl {
        url = "https://registry.npmjs.org/stream-chain/-/stream-chain-2.2.5.tgz";
        sha512 = "1TJmBx6aSWqZ4tx7aTpBDXK0/e2hhcNSTV8+CbFJtDjbb+I1mZ8lHit0Grw9GRT+6JbIrrDd8esncgBi8aBXGA==";
      };
    };
    "stream-json-1.9.1" = {
      name = "stream-json";
      packageName = "stream-json";
      version = "1.9.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/stream-json/-/stream-json-1.9.1.tgz";
        sha512 = "uWkjJ+2Nt/LO9Z/JyKZbMusL8Dkh97uUBTv3AJQ74y07lVahLY4eEFsPsE97pxYBwr8nnjMAIch5eqI0gPShyw==";
      };
    };
    "string-width-4.2.3" = {
      name = "string-width";
      packageName = "string-width";
      version = "4.2.3";
      src = fetchurl {
        url = "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz";
        sha512 = "wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==";
      };
    };
    "string_decoder-1.1.1" = {
      name = "string_decoder";
      packageName = "string_decoder";
      version = "1.1.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz";
        sha512 = "n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==";
      };
    };
    "strip-ansi-6.0.1" = {
      name = "strip-ansi";
      packageName = "strip-ansi";
      version = "6.0.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz";
        sha512 = "Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==";
      };
    };
    "strip-final-newline-2.0.0" = {
      name = "strip-final-newline";
      packageName = "strip-final-newline";
      version = "2.0.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-2.0.0.tgz";
        sha512 = "BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==";
      };
    };
    "supports-color-7.2.0" = {
      name = "supports-color";
      packageName = "supports-color";
      version = "7.2.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz";
        sha512 = "qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==";
      };
    };
    "telnet-client-1.2.8" = {
      name = "telnet-client";
      packageName = "telnet-client";
      version = "1.2.8";
      src = fetchurl {
        url = "https://registry.npmjs.org/telnet-client/-/telnet-client-1.2.8.tgz";
        sha512 = "W+w4k3QAmULVNhBVT2Fei369kGZCh/TH25M7caJAXW+hLxwoQRuw0di3cX4l0S9fgH3Mvq7u+IFMoBDpEw/eIg==";
      };
    };
    "temp-dir-1.0.0" = {
      name = "temp-dir";
      packageName = "temp-dir";
      version = "1.0.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/temp-dir/-/temp-dir-1.0.0.tgz";
        sha512 = "xZFXEGbG7SNC3itwBzI3RYjq/cEhBkx2hJuKGIUOcEULmkQExXiHat2z/qkISYsuR+IKumhEfKKbV5qXmhICFQ==";
      };
    };
    "tempfile-2.0.0" = {
      name = "tempfile";
      packageName = "tempfile";
      version = "2.0.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/tempfile/-/tempfile-2.0.0.tgz";
        sha512 = "ZOn6nJUgvgC09+doCEF3oB+r3ag7kUvlsXEGX069QRD60p+P3uP7XG9N2/at+EyIRGSN//ZY3LyEotA1YpmjuA==";
      };
    };
    "text-hex-1.0.0" = {
      name = "text-hex";
      packageName = "text-hex";
      version = "1.0.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/text-hex/-/text-hex-1.0.0.tgz";
        sha512 = "uuVGNWzgJ4yhRaNSiubPY7OjISw4sw4E5Uv0wbjp+OzcbmVU/rsT8ujgcXJhn9ypzsgr5vlzpPqP+MBBKcGvbg==";
      };
    };
    "trace-event-lib-1.4.1" = {
      name = "trace-event-lib";
      packageName = "trace-event-lib";
      version = "1.4.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/trace-event-lib/-/trace-event-lib-1.4.1.tgz";
        sha512 = "TOgFolKG8JFY+9d5EohGWMvwvteRafcyfPWWNIqcuD1W/FUvxWcy2MSCZ/beYHM63oYPHYHCd3tkbgCctHVP7w==";
      };
    };
    "triple-beam-1.4.1" = {
      name = "triple-beam";
      packageName = "triple-beam";
      version = "1.4.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/triple-beam/-/triple-beam-1.4.1.tgz";
        sha512 = "aZbgViZrg1QNcG+LULa7nhZpJTZSLm/mXnHXnbAbjmN5aSa0y7V+wvv6+4WaBtpISJzThKy+PIPxc1Nq1EJ9mg==";
      };
    };
    "truncate-utf8-bytes-1.0.2" = {
      name = "truncate-utf8-bytes";
      packageName = "truncate-utf8-bytes";
      version = "1.0.2";
      src = fetchurl {
        url = "https://registry.npmjs.org/truncate-utf8-bytes/-/truncate-utf8-bytes-1.0.2.tgz";
        sha512 = "95Pu1QXQvruGEhv62XCMO3Mm90GscOCClvrIUwCM0PYOXK3kaF3l3sIHxx71ThJfcbM2O5Au6SO3AWCSEfW4mQ==";
      };
    };
    "tslib-2.8.1" = {
      name = "tslib";
      packageName = "tslib";
      version = "2.8.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz";
        sha512 = "oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==";
      };
    };
    "type-fest-0.20.2" = {
      name = "type-fest";
      packageName = "type-fest";
      version = "0.20.2";
      src = fetchurl {
        url = "https://registry.npmjs.org/type-fest/-/type-fest-0.20.2.tgz";
        sha512 = "Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==";
      };
    };
    "universalify-2.0.1" = {
      name = "universalify";
      packageName = "universalify";
      version = "2.0.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/universalify/-/universalify-2.0.1.tgz";
        sha512 = "gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==";
      };
    };
    "utf8-byte-length-1.0.5" = {
      name = "utf8-byte-length";
      packageName = "utf8-byte-length";
      version = "1.0.5";
      src = fetchurl {
        url = "https://registry.npmjs.org/utf8-byte-length/-/utf8-byte-length-1.0.5.tgz";
        sha512 = "Xn0w3MtiQ6zoz2vFyUVruaCL53O/DwUvkEeOvj+uulMm0BkUGYWmBYVyElqZaSLhY6ZD0ulfU3aBra2aVT4xfA==";
      };
    };
    "util-deprecate-1.0.2" = {
      name = "util-deprecate";
      packageName = "util-deprecate";
      version = "1.0.2";
      src = fetchurl {
        url = "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz";
        sha512 = "EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==";
      };
    };
    "uuid-3.4.0" = {
      name = "uuid";
      packageName = "uuid";
      version = "3.4.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/uuid/-/uuid-3.4.0.tgz";
        sha512 = "HjSDRw6gZE5JMggctHBcjVak08+KEVhSIiDzFnT9S9aegmp85S/bReBVTb4QTFaRNptJ9kuYaNhnbNEOkbKb/A==";
      };
    };
    "which-1.3.1" = {
      name = "which";
      packageName = "which";
      version = "1.3.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/which/-/which-1.3.1.tgz";
        sha512 = "HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==";
      };
    };
    "which-2.0.2" = {
      name = "which";
      packageName = "which";
      version = "2.0.2";
      src = fetchurl {
        url = "https://registry.npmjs.org/which/-/which-2.0.2.tgz";
        sha512 = "BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==";
      };
    };
    "winston-3.17.0" = {
      name = "winston";
      packageName = "winston";
      version = "3.17.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/winston/-/winston-3.17.0.tgz";
        sha512 = "DLiFIXYC5fMPxaRg832S6F5mJYvePtmO5G9v9IgUFPhXm9/GkXarH/TUrBAVzhTCzAj9anE/+GjrgXp/54nOgw==";
      };
    };
    "winston-transport-4.9.0" = {
      name = "winston-transport";
      packageName = "winston-transport";
      version = "4.9.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/winston-transport/-/winston-transport-4.9.0.tgz";
        sha512 = "8drMJ4rkgaPo1Me4zD/3WLfI/zPdA9o2IipKODunnGDcuqbHwjsbB79ylv04LCGGzU0xQ6vTznOMpQGaLhhm6A==";
      };
    };
    "wrap-ansi-7.0.0" = {
      name = "wrap-ansi";
      packageName = "wrap-ansi";
      version = "7.0.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz";
        sha512 = "YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==";
      };
    };
    "wrappy-1.0.2" = {
      name = "wrappy";
      packageName = "wrappy";
      version = "1.0.2";
      src = fetchurl {
        url = "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz";
        sha512 = "l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==";
      };
    };
    "ws-7.5.10" = {
      name = "ws";
      packageName = "ws";
      version = "7.5.10";
      src = fetchurl {
        url = "https://registry.npmjs.org/ws/-/ws-7.5.10.tgz";
        sha512 = "+dbF1tHwZpXcbOJdVOkzLDxZP1ailvSxM6ZweXTegylPny803bFhA+vqBYw4s31NSAk4S2Qz+AKXK9a4wkdjcQ==";
      };
    };
    "y18n-5.0.8" = {
      name = "y18n";
      packageName = "y18n";
      version = "5.0.8";
      src = fetchurl {
        url = "https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz";
        sha512 = "0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==";
      };
    };
    "yargs-17.7.2" = {
      name = "yargs";
      packageName = "yargs";
      version = "17.7.2";
      src = fetchurl {
        url = "https://registry.npmjs.org/yargs/-/yargs-17.7.2.tgz";
        sha512 = "7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==";
      };
    };
    "yargs-parser-21.1.1" = {
      name = "yargs-parser";
      packageName = "yargs-parser";
      version = "21.1.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz";
        sha512 = "tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==";
      };
    };
    "yargs-unparser-2.0.0" = {
      name = "yargs-unparser";
      packageName = "yargs-unparser";
      version = "2.0.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/yargs-unparser/-/yargs-unparser-2.0.0.tgz";
        sha512 = "7pRTIA9Qc1caZ0bZ6RYRGbHJthJWuakf+WmHK0rVeLkNrrGhfoabBNdue6kdINI6r4if7ocq9aD/n7xwKOdzOA==";
      };
    };
    "yocto-queue-0.1.0" = {
      name = "yocto-queue";
      packageName = "yocto-queue";
      version = "0.1.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz";
        sha512 = "rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==";
      };
    };
  };
in
{
  "detox-20.38.0" = nodeEnv.buildNodePackage {
    name = "detox";
    packageName = "detox";
    version = "20.38.0";
    src = fetchurl {
      url = "https://registry.npmjs.org/detox/-/detox-20.38.0.tgz";
      sha512 = "7IGEEpz67yXrxt0ymtGTPBJ6NTVCMhu65alCBrF4cqmseXXAJYdh+SKo7lDYo6Nwr7TNa9iUhRxnCASCNSGQsw==";
    };
    dependencies = [
      sources."@colors/colors-1.6.0"
      sources."@dabh/diagnostics-2.0.3"
      sources."@flatten-js/interval-tree-1.1.3"
      sources."@types/triple-beam-1.3.5"
      sources."@wix-pilot/core-3.3.2"
      sources."@wix-pilot/detox-1.0.11"
      sources."ajv-8.17.1"
      sources."ansi-regex-5.0.1"
      sources."ansi-styles-4.3.0"
      sources."async-3.2.6"
      sources."balanced-match-1.0.2"
      sources."bluebird-3.7.2"
      sources."brace-expansion-2.0.1"
      sources."browser-process-hrtime-1.0.0"
      sources."bunyamin-1.6.3"
      sources."bunyan-1.8.15"
      sources."bunyan-debug-stream-3.1.1"
      sources."caf-15.0.1"
      sources."camelcase-6.3.0"
      sources."chalk-4.1.2"
      sources."cliui-8.0.1"
      (sources."color-3.2.1" // {
        dependencies = [
          sources."color-convert-1.9.3"
          sources."color-name-1.1.3"
        ];
      })
      sources."color-convert-2.0.1"
      sources."color-name-1.1.4"
      sources."color-string-1.9.1"
      sources."colorspace-1.1.4"
      sources."concat-map-0.0.1"
      sources."core-util-is-1.0.3"
      (sources."cross-spawn-7.0.6" // {
        dependencies = [
          sources."which-2.0.2"
        ];
      })
      sources."decamelize-4.0.0"
      sources."dtrace-provider-0.8.8"
      sources."duplexer2-0.1.4"
      sources."easy-stack-1.0.1"
      sources."emoji-regex-8.0.0"
      sources."enabled-2.0.0"
      sources."escalade-3.2.0"
      sources."event-pubsub-4.3.0"
      sources."execa-5.1.1"
      sources."exeunt-1.1.0"
      sources."fast-deep-equal-3.1.3"
      sources."fast-uri-3.0.6"
      sources."fecha-4.2.3"
      sources."find-up-5.0.0"
      sources."flat-5.0.2"
      sources."fn.name-1.1.0"
      sources."fs-extra-11.3.0"
      sources."fs.realpath-1.0.0"
      sources."funpermaproxy-1.1.0"
      sources."get-caller-file-2.0.5"
      sources."get-stream-6.0.1"
      sources."glob-8.1.0"
      sources."graceful-fs-4.2.11"
      sources."has-flag-4.0.0"
      sources."human-signals-2.1.0"
      sources."inflight-1.0.6"
      sources."inherits-2.0.4"
      sources."ini-1.3.8"
      sources."is-arrayish-0.3.2"
      sources."is-fullwidth-code-point-3.0.0"
      sources."is-plain-obj-2.1.0"
      sources."is-stream-2.0.1"
      sources."isarray-1.0.0"
      sources."isexe-2.0.0"
      (sources."jest-environment-emit-1.0.8" // {
        dependencies = [
          sources."bunyan-2.0.5"
        ];
      })
      sources."js-message-1.0.7"
      sources."js-queue-2.0.2"
      sources."json-cycle-1.5.0"
      sources."json-schema-traverse-1.0.0"
      sources."jsonfile-6.1.0"
      sources."kuler-2.0.0"
      sources."locate-path-6.0.0"
      sources."lodash-4.17.21"
      sources."lodash.merge-4.6.2"
      sources."logform-2.7.0"
      sources."merge-stream-2.0.0"
      sources."mimic-fn-2.1.0"
      sources."minimatch-5.1.6"
      sources."minimist-1.2.8"
      sources."mkdirp-0.5.6"
      sources."moment-2.30.1"
      sources."ms-2.1.3"
      sources."multi-sort-stream-1.0.4"
      sources."multipipe-4.0.0"
      sources."mv-2.1.1"
      sources."nan-2.22.2"
      sources."ncp-2.0.0"
      sources."node-ipc-9.2.1"
      sources."npm-run-path-4.0.1"
      sources."object-assign-4.1.1"
      sources."once-1.4.0"
      sources."one-time-1.0.0"
      sources."onetime-5.1.2"
      sources."p-limit-3.1.0"
      sources."p-locate-5.0.0"
      sources."path-exists-4.0.0"
      sources."path-is-absolute-1.0.1"
      sources."path-key-3.1.1"
      sources."pngjs-7.0.0"
      sources."process-nextick-args-2.0.1"
      sources."promisify-child-process-4.1.2"
      sources."proper-lockfile-3.2.0"
      sources."readable-stream-2.3.8"
      sources."require-directory-2.1.1"
      sources."require-from-string-2.0.2"
      sources."resolve-from-5.0.0"
      sources."retry-0.12.0"
      (sources."rimraf-2.4.5" // {
        dependencies = [
          sources."brace-expansion-1.1.11"
          sources."glob-6.0.4"
          sources."minimatch-3.1.2"
        ];
      })
      sources."safe-buffer-5.1.2"
      sources."safe-json-stringify-1.2.0"
      sources."safe-stable-stringify-2.5.0"
      sources."sanitize-filename-1.6.3"
      sources."semver-7.7.2"
      sources."serialize-error-8.1.0"
      sources."shebang-command-2.0.0"
      sources."shebang-regex-3.0.0"
      sources."shell-quote-1.8.3"
      sources."signal-exit-3.0.7"
      sources."simple-swizzle-0.2.2"
      sources."stack-trace-0.0.10"
      sources."stream-chain-2.2.5"
      sources."stream-json-1.9.1"
      sources."string-width-4.2.3"
      sources."string_decoder-1.1.1"
      sources."strip-ansi-6.0.1"
      sources."strip-final-newline-2.0.0"
      sources."supports-color-7.2.0"
      sources."telnet-client-1.2.8"
      sources."temp-dir-1.0.0"
      sources."tempfile-2.0.0"
      sources."text-hex-1.0.0"
      sources."trace-event-lib-1.4.1"
      sources."triple-beam-1.4.1"
      sources."truncate-utf8-bytes-1.0.2"
      sources."tslib-2.8.1"
      sources."type-fest-0.20.2"
      sources."universalify-2.0.1"
      sources."utf8-byte-length-1.0.5"
      sources."util-deprecate-1.0.2"
      sources."uuid-3.4.0"
      sources."which-1.3.1"
      (sources."winston-3.17.0" // {
        dependencies = [
          sources."readable-stream-3.6.2"
        ];
      })
      (sources."winston-transport-4.9.0" // {
        dependencies = [
          sources."readable-stream-3.6.2"
        ];
      })
      sources."wrap-ansi-7.0.0"
      sources."wrappy-1.0.2"
      sources."ws-7.5.10"
      sources."y18n-5.0.8"
      sources."yargs-17.7.2"
      sources."yargs-parser-21.1.1"
      sources."yargs-unparser-2.0.0"
      sources."yocto-queue-0.1.0"
    ];
    buildInputs = globalBuildInputs;
    meta = {
      description = "E2E tests and automation for mobile";
      homepage = "https://github.com/wix/Detox#readme";
      license = "MIT";
    };
    production = true;
    bypassCache = true;
    reconstructLock = true;
  };
}
