{ pkgs }:
let
  lighthouse_v9 = pkgs.callPackage ./lighthouse_v9 {};
  apk-mitm = pkgs.callPackage ./apk-mitm {};
  ws-reconnect-proxy = pkgs.callPackage ./ws-reconnect-proxy {};
  detox = pkgs.callPackage ./detox {};
  percy-cli = pkgs.callPackage ./percy-cli {};
  insecure-websocket-proxy = pkgs.callPackage ./insecure-websocket-proxy { nodejs = pkgs.nodejs-14_x; };
in
{
  lighthouse_v9 = lighthouse_v9.nodeDependencies;
  apk-mitm = apk-mitm.nodeDependencies;
  ws-reconnect-proxy = ws-reconnect-proxy."ws-reconnect-proxy-browserstack/ws-reconnect-proxy";
  detox = detox."detox-20.38.0";
  percy-cli = percy-cli.nodeDependencies;
  insecure-websocket-proxy = insecure-websocket-proxy.package;
}
