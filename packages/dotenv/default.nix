{ pkgs }:

with pkgs;
let
  adbPackage = callPackage ../adb {};
  npm_modules = callPackage ../npm_modules {};
in

[
  {
    package = writeTextFile {
      name = ".env";
      text = ''
        NODE_6_PATH="${nodejs-6_x}"
        NODE_8_PATH="${nodejs-8_x}"
        NODE_12_PATH="${nodejs-12_x}"
        NODE_14_PATH="${nodejs-14_x}"
        NODE_16_PATH="${nodejs-16_x}"
        NODE_20_PATH="${nodejs-20_x}"
        ADB="${adbPackage}/bin/adb"
        PRIVOXY="${privoxy_3_0_29}/bin/privoxy"
        PYTHON_3_6_PATH="${pkgs.python3_6_x}"
        MITMDUMP="${pkgs.mitmproxy_5}"/bin/mitmdump
        MITMDUMP_10="${pkgs.mitmproxy_10}"/bin/mitmdump
        QTFASTSTART="${pkgs.qtfaststart}"/bin/qtfaststart
        FFMPEG="${ffmpeg_2}/bin/ffmpeg"
        FFPROBE="${ffmpeg_2}/bin/ffprobe"
        SCRCPY="${pkgs.scrcpy}/bin/scrcpy"
        LIGHTHOUSE_V9="${npm_modules.lighthouse_v9}/lib"
        WS_RECONNECT_PROXY="${npm_modules.ws-reconnect-proxy}/lib/node_modules/ws-reconnect-proxy"
        INSECURE_WEBSOCKET_PROXY="${npm_modules.insecure-websocket-proxy}/lib/node_modules/insecure-websocket-proxy/main.js"
        APK_MITM="${npm_modules.apk-mitm}/bin/apk-mitm"
        DETOX_CLI="${npm_modules.detox}/bin/detox"
        APKTOOL="${pkgs.apktool}/bin/apktool"
        JAVA_7_HOME_PATH="${jre7}"
        JAVA_8_HOME_PATH="${jre8}"
        JARSIGNER="${jdk7}/bin/jarsigner"
        IMAGEMAGICK_CONVERT="${pkgs.imagemagick}/bin/convert"
        PSTREE="${pstree}/bin/pstree"
        SOCAT="${pkgs.socat}/bin/socat"
        CONNTRACK="${pkgs.conntrack-tools}/bin/conntrack"
      '';
    };
    # to install relative to where `bro install` is invoked
    # also can be a full path
    destination = ".env";
  }
]
