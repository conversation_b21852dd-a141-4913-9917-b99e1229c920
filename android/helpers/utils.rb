require_relative "./bootsnap_helper"
BootsnapHelper.enable_bootsnap

require 'English'
require 'bsdwh'
require 'android_toolkit'
require 'json'
require 'open3'
require 'aws-sdk-s3'
require 'fileutils'
require 'logger'
require 'static_conf'

require_relative '../../common/utils/yaml_parser'
require_relative '../constants'
require 'browserstack_logger'
require_relative '../../common/push_to_zombie'
require "/usr/local/.browserstack/mobile/common/helpers"
require_relative "../lib/browserstack_watcher_helper"
require_relative '../lib/os_utils'
require 'dotenv/load'
require_relative '../lib/process_reaper'
require_relative "./popup_helper"
require_relative '../models/android_device'
require_relative '../lib/root_command'
require "/usr/local/.browserstack/mobile-common/utils/constants"
require_relative '../../common/helpers'
require_relative 'fullcone_nat_manager'

def adb(device)
  AndroidToolkit::ADB.new(udid: device, path: BrowserStack::ADB)
end

def read_interaction_json
  JSON.parse(
    File.read(BrowserStack::INTERACTION_CONFIG_FILE)
  )["deviceConfigurations"]
rescue StandardError
  {}
end

def write_rtc_params(params)
  device_id = params[:device]
  begin
    device_name = params[:device_name].downcase
  rescue StandardError
    device_name = nil
  end
  stream_width = 384

  if device_name || !device_name == ''
    interaction_json = read_interaction_json
    interaction_params = interaction_json[device_name] || interaction_json['default']
    interaction_args = " #{interaction_params['touch_need_btn_touch_event'].to_i} " \
      "#{interaction_params['touch_avg_pressure'].to_i} " \
      "#{interaction_params['touch_avg_contact_size'].to_i} " \
      "#{interaction_params['touch_avg_finger_size'].to_i} " \
      "#{interaction_params['touch_max_track_id'].to_i}"
    interaction_args.gsub!('null', '0')
    stream_width = interaction_params['stream_width'] if interaction_params.key?('stream_width')
  else
    interaction_args = ''
  end

  # Make a shallow copy to avoid modifying the original params hash during iteration
  safe_params = params.dup
  safe_params[:noTrickle] ||= false

  if safe_params[:genre] == 'app_automate'
    safe_params[:packageName] = safe_params[:app_testing_bundle_id].to_s
    safe_params[:android_version] = safe_params[:device_version]
  end

  safe_params[:cls_servers] = []
  if safe_params.key?('zombieHost')
    zombie_host = safe_params[:zombieHost].split(':')
    safe_params[:cls_servers] << {
      'type' => 'ZOMBIE',
      'host' => zombie_host[0],
      'port' => zombie_host[1].to_i
    }
  end

  if safe_params.key?('logHost')
    loghost = safe_params[:logHost].split(':')
    safe_params[:cls_servers] << { 'type' => 'CLS', 'host' => loghost[0], 'port' => loghost[1].to_i }
  end

  writeable_params = {}
  safe_params.each_key do |key|
    writeable_params[key] = safe_params[key] unless BLACKLISTED_RTC_PARAMS.include?(key)
  end

  File.open(
    "#{BrowserStack::CONFIG_DIR}/rtc_service_#{device_id}", 'w+'
  ) { |file| file.write(writeable_params.to_json) }

  { interaction_args: interaction_args, stream_width: stream_width }
end

def get_stream_width(device_name)
  interaction_json = read_interaction_json
  interaction_params = interaction_json[device_name] || interaction_json['default']
  interaction_params['stream_width']
end

def calc_tun_counter(port)
  (port.to_i - 8078)
end

def calc_usb_tunnel_ip(port)
  ip_indx = port.to_i - 8078
  "10.0.0.#{ip_indx}"
end

def spawn_process(cmd)
  pid = spawn(cmd)
  Process.detach(pid)
  pid
end

# Spawns a new process while using a common thread for reaping
def spawn_with_reaper(cmd)
  pid = spawn(cmd)
  ProcessReaper.async_reap(pid)
end

# Deprecated, use spawn_process instead
def fork_process(cmd)
  spawn_process(cmd)
end

def set_device_locale(device, locale, logger, async: true)
  cmd = "timeout 5 adb -s #{device} shell <<__EOF
settings put global hidden_api_policy 1;
pm grant com.android.browserstack android.permission.CHANGE_CONFIGURATION;
am startservice --user 0 -n com.android.browserstack/.services.NetworkService --es task \
\"change_language\" --es l \"#{locale}\";
exit;
__EOF"
  logger.info("Changing device language: #{cmd}")
  if async
    spawn_process(cmd)
  else
    `#{cmd}`
    device_locale = adb(device).getprop("persist.sys.locale")
    logger.info("locale: #{locale} device_locale: #{device_locale}")
    # In some scenarios like if input locale is zh-HANS then device_locale would be zh and not zh-HANS
    # therefore we cannot use locale == device_locale
    locale.tr("-", "_").to_s.downcase.start_with?(device_locale.tr("-", "_").to_s.downcase) && !device_locale.empty?
  end
end

def get_repeater_host_from_privoxy_config(device_identifier)
  privoxy_config_path = "/usr/local/.browserstack/config/#{device_identifier}.txt"
  privoxy_config = File.read(privoxy_config_path)
  privoxy_lines = privoxy_config.split("\n")
  privoxy_lines.each do |line|
    return line.match(/([^@\s]+):\d+/)[1] if line =~ /^forward-socks5/
  end
  nil
end

def run_fullcone_script(device_id)
  device_obj = BrowserStack::AndroidDevice.new(device_id, "utils.rb - run_fullcone_script", BrowserStack.logger)
  tun_interface_ip = device_obj.vpn_ip

  # Get the singleton manager instance
  manager = FullconeNATManager.instance

  # Get or create a FullconeNat instance for this tun_interface_ip
  fullcone_nat = manager.get_or_create(tun_interface_ip)

  # Start the FullconeNat service
  if fullcone_nat.start
    BrowserStack.logger.info("FullconeNat started successfully for #{tun_interface_ip}")
    true
  else
    BrowserStack.logger.error("Failed to start FullconeNat for #{tun_interface_ip}")
    false
  end
rescue StandardError => e
  BrowserStack.logger.error("Error in run_fullcone_script: #{e.message} #{e.backtrace}")
  false
end

def redact_params(params, request=nil)
  keys_to_redact = [
    'app_store_username',
    'app_store_password',
    'video_aws_keys',
    'video_aws_secret',
    's3_app_url',
    'appiumlogs_aws_secret',
    'stats_aws_secret',
    'password'
  ]
  log_params = params.clone
  if !request.nil? && request.request_method.upcase == "POST" && request.url.include?("app_live/inject_pfx_file")
    request_body = begin
      JSON.parse(request.body.read)
    rescue StandardError
      {}
    end
    request_body_key = log_params.keys[1]
    log_params.delete(request_body_key)
    log_params.merge!(request_body)
  end
  keys_to_redact.each do |key|
    log_params[key] = '[REDACTED]' if log_params.key?(key)
  end
  log_params
end

def push_privoxy_stats_to_repeater(device_identifier, session_id, privoxy_stats, logger)
  repeater_host = get_repeater_host_from_privoxy_config(device_identifier)
  return if repeater_host.nil?

  begin
    Timeout.timeout(1) do
      uri_path = "/update_privoxy_stats/#{session_id}"
      Net::HTTP.new(repeater_host, 80).start do |http|
        request = Net::HTTP::Post.new(uri_path, 'Content-Type' => 'application/json')
        request.body = privoxy_stats.to_json
        http.request(request)
      end
    end
  rescue Timeout::Error
    logger.info('Timeout while posting privoxy stats to repeater in 10 seconds')
  rescue StandardError => e
    logger.info(
      "Unable to post privoxy stats to repeater - #{e.message} \n\n#{e.backtrace.join("\n")}"
    )
  end
end

def device_object
  @device_object ||= BrowserStack::AndroidDevice.new(@device, self.class.to_s, BrowserStack.logger)
end

def root_command
  @root_command ||= RootCommand.new(device_object, BrowserStack.logger)
end

def is_aa_framework?(framework)
  ["espresso", "fluttertest"].include?(framework)
end

def find_current_focus(device)
  current_focus = "adb -s #{device} shell dumpsys window | grep -E 'mCurrentFocus'"
  OSUtils.execute(current_focus)
end

def home_screen_focused?(device)
  find_current_focus(device).include?("launcher")
end

def press_home_screen_button(device)
  BrowserStack.logger.info "Pressing home button"
  adb(device).shell("input keyevent KEYCODE_HOME")   # Press home button
  sleep 1
end

def handle_popup_via_adb_input(device)
  adb_object = adb(device)
  adb_object.shell("input keyevent 62")    # Press space button
  adb_object.shell("input keyevent 22")    # Press right arrow key
  adb_object.shell("input keyevent 62")    # Press space button
  sleep 1
end

def disable_and_enable_rtc2_app(device)
  BrowserStack.logger.info("Disabling RTC2 app")
  if bsrun_device?(device)
    BrowserStack.logger.info "Device is rooted, using pm disable"
    root_command.run("pm disable fr.pchab.AndroidRTC2")

    sleep 0.5

    BrowserStack.logger.info("Enabling RTC2 app")
    root_command.run("pm enable fr.pchab.AndroidRTC2")
  else
    cmd = "adb -s #{device} shell pm disable-user fr.pchab.AndroidRTC2"
    output, status = OSUtils.execute(cmd, true)
    BrowserStack.logger.info("Disabled RTC2 app to dismiss media projection popup, output: #{output}")

    sleep 0.5

    BrowserStack.logger.info("Enabling RTC2 app")
    cmd = "adb -s #{device} shell pm enable fr.pchab.AndroidRTC2"
    output, status = OSUtils.execute(cmd, true)
    BrowserStack.logger.info("Enabled RTC2 app, output: #{output}")
  end
end

def handle_media_projection_popup_via_coordinates(device, os_version, mp_popup_coords = nil)
  coordinates_hash = JSON[File.read(BrowserStack::MEDIA_PROJECTION_COORDINATES_JSON)]
  model = adb(device).model

  if coordinates_hash[model]
    coordinates = coordinates_hash[model][os_version.to_s.to_f.to_s]
    unless coordinates.nil?
      BrowserStack.logger.info("Tapping at #{coordinates}, fetched from json")
      adb(device).shell("input tap #{coordinates}")
      sleep 1
      return true
    end
  end

  # If JSON didn't have coordinates, fallback to passed-in mp_popup_coords
  unless mp_popup_coords.nil?
    BrowserStack.logger.info("Tapping at #{mp_popup_coords}, passed via param")
    if mp_popup_coords.is_a?(Array)
      mp_popup_coords.each do |coord|
        BrowserStack.logger.info("Tapping #{coord}")
        adb(device).shell("input tap #{coord}")
        sleep 0.5
      end
    else
      adb(device).shell("input tap #{mp_popup_coords}")
      sleep 1
    end
    return true
  end

  false
rescue StandardError => e
  BrowserStack.logger.info("Failed to tap on media projection popup: #{e.message}")
  false
end

def media_projection_uiautomation_cmd(device, os_version)
  model = adb(device).model
  automation_class = 'PopUpHandlerTest'
  automation_class = 'MediaProjectionHandlerTest' if Gem::Version.new(os_version) >= Gem::Version.new('15') || [
'Pixel 9', 'Pixel 9 Pro XL'
].include?(model)
  "adb -s #{device} shell \"am instrument -w -r -e debug false -e class "\
  "'com.browserstack.uiautomation.#{automation_class}' "\
  "com.browserstack.uiautomation.test/androidx.test.runner.AndroidJUnitRunner\""
end

def handle_automate_media_projection_popup(device, params, os_version)
  handle_attempts = 0
  start_time = Time.now
  attempt = 20
  pushed_automation_failure_to_zombie = false
  session_id = params['automate_session_id']
  framework = params['framework'] || ""
  device_obj = BrowserStack::AndroidDevice.new(device, "Utils.rb", BrowserStack.logger)
  perm_window_checker_thread = nil
  mp_popup_coords = params["media_projection_popup_coords"]

  while handle_attempts <= attempt
    perm_window = "adb -s #{device} shell dumpsys window "\
    " | grep -E 'mCurrentFocus|mFocusedApp|ActivityRecord' | grep \"MediaProjectionPermissionActivity\""
    cmd_output = system(perm_window)
    if cmd_output != true
      BrowserStack.logger.info "[Automate] Projection window not found, waiting."\
      "Attempt: #{handle_attempts}."
    else
      if is_aa_framework?(framework)
        perm_window_checker_thread ||= RtcHelper.look_for_video_start_time(perm_window, device)
      end

      BrowserStack.logger.info "[Automate] Projection window found. Attempt: "\
      "#{handle_attempts} for #{params['automate_session_id']}"
      cmd = media_projection_uiautomation_cmd(device, os_version)
      handler_output = `#{cmd}`
      end_time = Time.now - start_time
      push_to_cls(params, "handle_media_projection_popup", '', { "device" => device,
                                                                 "MediaProjectionHandledIn" => end_time,
                                                                 "MediaProjectionHandleAttempts" => handle_attempts })
      if system(perm_window)
        BrowserStack.logger.info "Popup still present after UI automation"
        BrowserStack.logger.info "Trying new popup dismiss mechanism"
        unless pushed_automation_failure_to_zombie
          # to avoid multiple pushing to zombie for retries
          session_id = params['automate_session_id']
          device_obj = BrowserStack::AndroidDevice.new(device, "Utils.rb", BrowserStack.logger)
          current_activity = device_obj.current_activity.join("\n")
          zombie_type = 'media-projection-automation-failure'
          zombie_push('android', zombie_type, cmd_output.to_s, current_activity, device, session_id)
          pushed_automation_failure_to_zombie = true
        end
        popup_handler = BrowserStack::PopupHelper.new(
          device_id: device,
          os_version: Gem::Version.new(os_version),
          session_id: params['automate_session_id'],
          mp_popup_coords: mp_popup_coords
        )
        popup_handler.handle_media_projection_popup_via_coordinates
      end

      if system(perm_window) && is_aa_framework?(framework)
        BrowserStack.logger.info "Popup still present after adb tap"
        BrowserStack.logger.info "Trying new popup dismiss mechanism"

        # If popup is dismissed via this function without clicking on Start Now,
        # then move this fallback after handle_popup_via_adb_input
        handle_media_projection_popup_via_coordinates(device, os_version, mp_popup_coords)
      end

      if system(perm_window) && is_aa_framework?(framework)
        BrowserStack.logger.info "Popup still present after coordinates tap"
        BrowserStack.logger.info "Trying new popup dismiss mechanism"

        handle_popup_via_adb_input(device)
      end
      unless system(perm_window) # return only when popup has dismissed
        return unless is_aa_framework?(framework)

        BrowserStack.logger.info "Waiting for thread to exit, thread status: #{perm_window_checker_thread&.status}"
        perm_window_checker_thread&.join
        BrowserStack.logger.info "Proceeding to start test execution"

        return true
      end

      BrowserStack.logger.info "[Automate] Projection window found but not handled. Attempt: #{handle_attempts}"\
      "#{handler_output}"
    end
    handle_attempts += 1
    sleep 0.5
  end

  # This block should not be executed when media projection popup didnot appear
  # perm_window_checker_thread will always be nil for interactive debugging sessions
  unless perm_window_checker_thread.nil?
    BrowserStack.logger.info "Killing thread: #{perm_window_checker_thread} as it will not exit by itself"
    Thread.kill(perm_window_checker_thread)
    BrowserStack.logger.info "Thread killed, thread status: #{perm_window_checker_thread.status}"

    BrowserStack.logger.info "Dismissing media projection popup to bring app under test to foreground"\
    " and continue with fallback"
    disable_and_enable_rtc2_app(device)
  end

  # Handle any edge case if some popup or app is opened
  unless home_screen_focused?(device) && is_aa_framework?(framework)
    BrowserStack.logger.info "Home screen is not on focus, handling popup and clicking on home button"
    handle_popup_via_adb_input(device)
    press_home_screen_button(device)
  end

  screen_on = device_obj.screen_on?
  current_activity = device_obj.current_activity.join("\n")
  if handle_attempts == (attempt + 1)
    device_obj.capture_screenshot_and_pull("/tmp/media_projection_automation_fail_#{session_id}_#{handle_attempts}.png")
  end
  zombie_type = if perm_window_checker_thread.nil?
                  'media-projection-popup-failure'
                else
                  'media-projection-popup-handling-failure'
                end
  zombie_push('android', zombie_type, cmd_output.to_s, screen_on, current_activity, device, session_id)
  false

rescue StandardError => e
  BrowserStack.logger.error "Some exception occured while handling media projection"\
  "popup #{e.message} - #{e.backtrace}"
  unless perm_window_checker_thread.nil?
    BrowserStack.logger.info "Thread status: #{perm_window_checker_thread&.status}"
    Thread.kill(perm_window_checker_thread)
  end
  disable_and_enable_rtc2_app(device)
  zombie_push('android', 'media-proj-popup-handling-exception', e.message, '', '', @device, @session_id)
  false
end

def send_network_logs_to_eds(session_id, genre, all_host_requests, key_config = {})
  eds = EDS.new(key_config, BrowserStack.logger)

  BrowserStack.logger.info "REGEX_TO_SKIP_FOR_NETWORK_LOGS[StartsWith]- #{REGEX_TO_SKIP_FOR_NETWORK_LOGS['StartsWith']}"
  BrowserStack.logger.info "REGEX_TO_SKIP_FOR_NETWORK_LOGS[ExactMatch]- #{REGEX_TO_SKIP_FOR_NETWORK_LOGS['ExactMatch']}"

  starts_with_skip_urls = REGEX_TO_SKIP_FOR_NETWORK_LOGS["StartsWith"].map { |url| "^" << url.gsub(".", "[.]") }
  exact_match_skip_urls = REGEX_TO_SKIP_FOR_NETWORK_LOGS["ExactMatch"].map { |url| url.gsub(".", "[.]") }

  BrowserStack.logger.info "starts_with_skip_urls - #{starts_with_skip_urls}"
  BrowserStack.logger.info "exact_match_skip_urls - #{exact_match_skip_urls}"

  urls_to_skip = starts_with_skip_urls + exact_match_skip_urls

  skip_urls = /#{urls_to_skip.join('|')}/

  BrowserStack.logger.info "skip_urls - #{skip_urls}"

  all_host_requests.each do |host|
    domain = host[0]

    # Skip if domain is part of skip urls or domain is an IP address
    next if skip_urls === domain || /^((25[0-5]|(2[0-4]|1\d|[1-9]|)\d)\.?\b){4}/m === domain  # rubocop:todo Style/CaseEquality

    data = {
      session_id: session_id,
      product: genre,
      url_accessed: domain,
      first_request_time: host[1]["first_request_time"],
      requests_count: host[1]["count"]
    }

    # TODO: Need to move the string to EdsConstants::SESSION_NETWORK_LOGS after bundle update
    eds.push_logs("session_network_logs", data)
  end

rescue StandardError => e
  BrowserStack.logger.error "Got error when pushing data to EDS: #{e.message} #{e.backtrace}"
end

def get_user_installed_apps
  # IMPORTANT: Referred from KNOWN_PLAYSTORE_INSTALLED_APPS, change that too if adding here
  to_be_ignored = [
    'com.google.android.instantapps.supervisor', 'com.google.android.googlequicksearchbox'
  ]
  device_version = adb(@device).os_version
  all_apps = if device_version >= Gem::Version.new("7")
               `timeout 5 adb -s #{@device} shell cmd package list packages -u -i -3`
             else
               `timeout 5 adb -s #{@device} shell pm list packages -u -i -3`
             end
  currently_present_apps = all_apps.split("\n").map { |a| a.split("package:")[1].split(" ")[0] }

  # Following filter is for auto-downloaded apps when user logs into playstore.
  # Happens for non-google devices.
  currently_present_apps = currently_present_apps.reject do |a|
    a.match(/^(com\.google\.android\.|com\.samsung\.|com\.sec\.)/)
  end

  begin
    previous_state_apps = File.open("/tmp/ALL_SYSTEM_AND_UPLOADED_APPS_#{@device}", 'r').readlines
  rescue StandardError
    previous_state_apps = currently_present_apps
  end
  previous_state_apps.map!(&:strip)
  currently_present_apps - previous_state_apps - to_be_ignored
end

def type_check(event_name, event_hash)
  event_name_check = [String, Symbol].include? event_name.class
  unless event_name_check
    BrowserStack.logger.error(
      "event_name must be a String or a Symbol but given #{event_name.class}"
    )
  end

  event_hash_check = event_hash.is_a?(Hash)
  BrowserStack.logger.error("event_hash must be a Hash but given #{event_hash.class}") unless event_hash_check

  event_name_check && event_hash_check
end

def mark_event_start(event_name, event_hash)
  return unless type_check(event_name, event_hash)

  event_hash[event_name] = (Time.now.to_f * 1000).to_i
end

def mark_event_end(event_name, event_hash)
  return unless type_check(event_name, event_hash)

  event_hash[event_name] = (Time.now.to_f * 1000).to_i - (
    event_hash[event_name] || event_hash[:absolute_start_time]
  )
end

def set_event_time(event_name, event_hash, event_time)
  return unless type_check(event_name, event_hash)

  event_hash[event_name] = (event_time * 1000).to_i
end

def get_session_id_from_params(params)
  # rubocop:disable Layout/LineLength
  params["app_live_session_id"] || params["live_session_id"] || params["automate_session_id"] || params["app_automate_session_id"]
  # rubocop:enable Layout/LineLength
end

# state can be 'old' or 'new'
def session_merge_info(params, state)
  session_id = get_session_id_from_params(params)
  user_id = params['user_id']
  genre = params['genre']
  rails_host = params['host']
  {
    "#{state}_session_id" => session_id,
    "#{state}_user_id" => user_id,
    "#{state}_genre" => genre,
    "#{state}_rails_host" => rails_host
  }.to_json
end

# We can use this function to send data to EDS
def send_attrs_to_eds(key, value, params, event_type)
  event = {
    'event_type' => event_type,
    'data' => {
      'timestamp' => Time.now.to_i,
      'hashed_id' => get_session_id_from_params(params),
      'source' => 'platform',
      'type' => 'start'
    }
  }
  event['data'][key] = value
  Bsdwh.send(event.to_json, params[:edsHost], params[:edsPort], params[:edsKey])
  BrowserStack.logger.info("Data sent to EDS: #{event}")
rescue StandardError => e
  BrowserStack.logger.error("Got #{e.message} #{e.backtrace} when pushing data to EDS")
end

def send_to_eds(params, event_type)
  params[:event_hash].delete(:absolute_start_time)
  event = {
    'event_type' => event_type,
    'data' => {
      'timestamp' => Time.now.to_i,
      'hashed_id' => params[:video_session_id],
      'source' => 'platform',
      'type' => 'start',
      'data' => convert_dot_to_nested_hash(params[:event_hash]).to_json
    }
  }
  Bsdwh.send(event.to_json, params[:edsHost], params[:edsPort], params[:edsKey])
  BrowserStack.logger.info("Data sent to EDS: #{event}")
rescue StandardError => e
  BrowserStack.logger.error("Got #{e.message} #{e.backtrace} when pushing data to EDS")
end

def convert_dot_to_nested_hash(dot_notation_hash)
  elements = dot_notation_hash.map do |main_key, main_value|
    main_key.to_s.split('.').reverse.inject(main_value) do |value, key|
      { key => value }
    end
  end
  elements.inject({}) { |final_hash, elem| final_hash.deep_merge!(elem) }
end

def read_instrumentation_logs_from_file(file_path, logger, parent="")
  result = {}
  begin
    File.open(file_path) do |file|
      logs = file.read.split("\n")[1..]
      logs.each do |log|
        key, value = log.split(' ')
        result["#{parent}.#{key.downcase}"] = value
      end
    end
  rescue StandardError => e
    logger.error("Can't read instrumentation logs from file: #{file_path} due to #{e.message}")
  end
  result
end

def log_repeater_connectivity(privoxy_port)
  Thread.bs_run do
    repeater_ping_curl_command = "curl -x 127.0.0.1:#{privoxy_port} --max-time 3 -A \"Mozilla/5.0 (Windows NT 6.1; "\
                                 "Win64; x64; rv:59.0) Gecko/20100101 Firefox/59.0 Android Zotak\" "\
                                 "http://ping-local.browserstack.com/local-debug/http"
    started_at = Time.now.to_f
    response = system(repeater_ping_curl_command)
    time_elapsed = ((Time.now.to_f - started_at) * 1000).to_i
    BrowserStack.logger.info "Local ping repeater command: #{repeater_ping_curl_command}. "\
                             "Response: #{response}. Time elapsed: #{time_elapsed}ms"
  end
end

def clean_in_session(device)
  system("timeout 3 #{BrowserStack::ADB} -s #{device} shell 'rm -f /sdcard/in_session'")
  # To remove any stale network_simulation mid session file if present
  FileUtils.rm_f("#{STATE_FILES_DIR}/network_simulation_mid_session_#{device}")
  # To remove any stale interactive session file file if present
  FileUtils.rm_f("#{STATE_FILES_DIR}/interactive_session_file_#{device}")
end

# Hash utilities
class Hash
  def deep_merge!(second)
    merger = proc { |_, v1, v2| v1.is_a?(Hash) && v2.is_a?(Hash) ? v1.merge(v2, &merger) : v2 }
    merge!(second, &merger)
  end
end

def write_to_file(filename, data, raw_format: false)
  data = data.to_json unless raw_format
  File.open(filename, 'w+') { |file| file.write(data) }
  BrowserStack.logger.info("File [#{filename}] written" )

  return if File.exist?(filename)

  BrowserStack.logger.info("File [#{filename}] write Failed" )
rescue StandardError => e
  BrowserStack.logger.error("Exception while writing #{filename}: #{e.message} #{e.backtrace}")
end

def check_firebase_and_billing_error(device, device_name, session_id, device_logs_file, user_id)

    # Sample log line : 05-05 10:20:37.874 W/FCM TOKEN Failed(  686): java.io.IOException: SERVICE_NOT_AVAILABLE
  service_output = `grep -m 1 "SERVICE_NOT_AVAILABLE" #{device_logs_file}`
  service_unavailable_code =  $CHILD_STATUS.exitstatus == 0 ? "service_unavailable" : nil

  # Sample log line: 05-05 10:15:51.846 W/FCM TOKEN Failed(13703): java.io.IOException: AUTHENTICATION_FAILED
  auth_output = `grep -m 1 -E "AUTHENTICATION_FAILED|FIS_AUTH_ERROR" #{device_logs_file}`
  auth_failed_code =  $CHILD_STATUS.exitstatus == 0 ? "auth_failed" : nil

  # 10-22 16:19:56.923 W/BillingClient( 6400): In-app billing API version 3 is not supported on this device.
  billing_output = `grep -m 1 "In-app billing API version 3 is not supported on this device" #{device_logs_file}`
  billing_error_code = $CHILD_STATUS.exitstatus == 0 ? "billing-error" : nil

  gms_version = `adb -s #{device} shell dumpsys package com.google.android.gms \
| grep -m 1 versionName | awk -F "=" '{print $2}' | awk '{print $1}'`
  gms_version = gms_version.delete("\n")

  playstore_version = `adb -s #{device} shell dumpsys package com.android.vending \
| grep -m 1 versionName | awk -F "=" '{print $2}' | awk '{print $1}'`
  playstore_version = playstore_version.delete("\n")

  # Sample log line : 10-07 10:46:57.991 I/FirebaseInitProvider(18917): FirebaseApp initialization successful
  firebase_used = `grep -m 1 "FirebaseApp initialization successful" #{device_logs_file}`
  firebase_used_code =  $CHILD_STATUS.exitstatus == 0 ? "firebase_used" : nil

  if firebase_used_code
    BrowserStack.logger.info("[firebase] Detected firebase use : gms_version #{gms_version} : playstore_version "\
                             "#{playstore_version} firebase_used : #{firebase_used}" )

    data = {
      "user_os" => gms_version,
      "user_browser" => playstore_version
    }
    zombie_push("android", "firebase-used", "FirebaseApp initialization successful", device_name, data,
                device, session_id, user_id)
  end

  # If either of the error found in device logs then get playstore and gms versions and send to zombie
  if service_unavailable_code || auth_failed_code
    BrowserStack.logger.info("[firebase] Detected error in firebase : #{service_unavailable_code} : "\
                             "#{auth_failed_code} : gms_version #{gms_version} : playstore_version "\
                             "#{playstore_version} service_output : #{service_output} ; auth_output: #{auth_output}" )

    data = {
      "user_os" => gms_version,
      "user_browser" => playstore_version,
      "user_browser_version" => auth_failed_code,
      "service_ouput" => service_output.to_s[0..150],
      "auth_ouput" => auth_output.to_s[0..150]
    }
    zombie_push("android", "firebase-issue", service_unavailable_code, device_name, data, device, session_id, user_id)
  else
    BrowserStack.logger.info("[firebase] No error because of firebase or billing : gms_version : #{gms_version} "\
                             ": playstore_version #{playstore_version}")
  end

  if billing_error_code
    BrowserStack.logger.info("[Billing] Detected error in billing : #{billing_error_code} : "\
           "gms_version #{gms_version} : playstore_version #{playstore_version} billing_output : #{billing_output}" )

    data = {
      "user_os" => gms_version,
      "user_browser" => playstore_version,
      "user_browser_version" => billing_error_code,
      "billing_output" => billing_output.to_s[0..150]
    }

    zombie_push("android", "billing-error", billing_error_code, device_name, data, device, session_id, user_id)
  end
rescue StandardError => e
  BrowserStack.logger.error("Exception : #{e.message} #{e.backtrace}")

end

def extract_session_specific_lines(session_id, logcat_file, session_details_file)
  system("fgrep", session_id, logcat_file, { out: [session_details_file, "w"] })
end

def check_for_midsession_chrome_and_webview_updates(device, device_name, session_id, session_details_file, user_id)

  `fgrep -m 1 "updatePackage:com.android.chrome" #{session_details_file}`
  chrome_update_code = $CHILD_STATUS.exitstatus == 0 ? "mid-session-chrome-update" : nil

  `fgrep -m 1 "Begin install of com.google.android.webview" #{session_details_file}`
  webview_update_code = $CHILD_STATUS.exitstatus == 0 ? "mid-session-webview-update" : nil

  if chrome_update_code || webview_update_code
    data = {}
    if chrome_update_code
      BrowserStack.logger.info("[mid-session-upgrade] Detected error in session : "\
                               "#{session_id} : mid-session-chrome-update" )
      zombie_push("android", "mid-session-chrome-update", "mid-session-chrome-update", device_name,
                  data, device, session_id, user_id)
    end
    if webview_update_code
      BrowserStack.logger.info("[mid-session-upgrade] Detected error in session : #{session_id} : "\
                               "mid-session-webview-update" )
      zombie_push("android", "mid-session-webview-update", "mid-session-webview-update", device_name,
                  data, device, session_id, user_id)
    end
  else
    BrowserStack.logger.info("[no-mid-session-upgrade] No error because of mid session chrome or webview upgrade")
  end

rescue StandardError => e
  BrowserStack.logger.error("Exception in check_for_midsession_chrome_and_webview_updates : "\
                            "#{e.message} #{e.backtrace}")

end

def check_zygote_connection_failure(device, device_name, session_id, session_details_file, user_id)

  `fgrep -m 1 "Error connecting to zygote" #{session_details_file}`
  zygote_connection_code = $CHILD_STATUS.exitstatus == 0 ? "zygote-connection-fail" : nil

  if zygote_connection_code
    BrowserStack.logger.info("[zygote-connection-fail] Detected error in session : #{session_id}" )
    data = {}
    zombie_push("android", "zygote-connection-fail", "zygote-connection-fail", device_name, data, device,
                session_id, user_id)
  else
    BrowserStack.logger.info("[zygote-connection-ok] No error because of zygote connection failure")
  end
rescue StandardError => e
  BrowserStack.logger.error("Exception in check_zygote_connection_failure : #{e.message} #{e.backtrace}")

end

def check_app_kill(device, device_name, session_id, session_details_file, user_id, main_app_bundle_id)

  app_killed_process = `fgrep Killing #{session_details_file} \
| fgrep #{main_app_bundle_id} | tail -1 | awk '{print $NF}'`
  if $CHILD_STATUS.exitstatus == 0
    BrowserStack.logger.info("App #{main_app_bundle_id} was last killed by process #{app_killed_process}")

    if !app_killed_process.to_s.include?(main_app_bundle_id.to_s)
      BrowserStack.logger.error("App was killed by another app #{app_killed_process}.")
      zombie_push("android", "app-killed-by-process", main_app_bundle_id.to_s[0..150], device_name,
                  app_killed_process.to_s[0..150], device, session_id, user_id)
    else
      BrowserStack.logger.error("App was killed by itself #{app_killed_process}.")
    end
  end
rescue StandardError => e
  BrowserStack.logger.error("Exception in check_app_kill : #{e.message} #{e.backtrace}")

end

# to check if wifi was toggled to off state mid session
def check_for_wifi_lost_issues(session_details_file, wifi_lost_logfile)
  if File.exist?(session_details_file)
    device_logs = File.readlines(session_details_file)
    wifi_lost_count = device_logs.grep(/BS_WATCHER.*NetworkMonitorService.*wifi lost/i).count
    File.write(wifi_lost_logfile, wifi_lost_count) if wifi_lost_count > 0
  end
end

def get_pattern_occurence_count(pattern, file, ignore_case: false)

  OSUtils.execute("grep -cE#{ignore_case ? 'i' : ''} '#{pattern}' '#{file}'").strip.to_i
rescue StandardError
  0
end

def get_pattern_occurence_count_with_grep(pattern, grep_pattern, file, ignore_case: false)

  OSUtils.execute(
    "grep -E#{ignore_case ? 'i' : ''} '#{grep_pattern}' '#{file}' |\
    grep -cE#{ignore_case ? 'i' : ''} '#{pattern}'"
  ).strip.to_i
rescue StandardError
  0
end

# Instrumentation to check whether there were any socket timeout's or socket exceptions
# TODO: Move this into internet_healthcheck.rb
def check_device_logs_network_issues(device, session_id, device_logs_file)
  exceptions = {}
  port = get_device_port(device)
  privoxy_port = calculate_privoxy_port(port)

  socket_exception_count = get_pattern_occurence_count('SocketTimeoutException', device_logs_file)
  socket_exception_ssl_count = get_pattern_occurence_count_with_grep('SSL handshake timed out',
                                                                     'SocketTimeoutException', device_logs_file)
  socket_exception_timeout_count = get_pattern_occurence_count_with_grep('SocketTimeoutException: timeout',
                                                                         'SocketTimeoutException', device_logs_file)
  socket_exception_read_timeout_count = get_pattern_occurence_count_with_grep(
    'Read timed out', 'SocketTimeoutException', device_logs_file
  )
  socket_exception_failed_to_connect_count = get_pattern_occurence_count_with_grep(
    'failed to connect', 'SocketTimeoutException', device_logs_file
  )
  socket_exception_java_ssl_exception_count = get_pattern_occurence_count(
    'javax.net.ssl.SSLHandshakeException', device_logs_file
  )
  http_exception_count = get_pattern_occurence_count('HTTP_EXCEPTION|ETIMEDOUT', device_logs_file)
  dns_exception_count = get_pattern_occurence_count('unable to resolve host', device_logs_file)

  failed_to_connect_count = get_pattern_occurence_count("failed to connect.*#{privoxy_port}", device_logs_file)
  ftc_update_app_pid_count = get_pattern_occurence_count_with_grep(
    'com.android.browserstack.utils.UpdateAppPid', "failed to connect.*#{privoxy_port}", device_logs_file
  )
  ftc_timeout_count = get_pattern_occurence_count_with_grep("failed to connect.*#{privoxy_port}.* after .*ms",
                                                            "failed to connect.*#{privoxy_port}", device_logs_file)

  ftc_count = failed_to_connect_count - ftc_update_app_pid_count - ftc_timeout_count

  exceptions["socket_exception_count"] = socket_exception_count if socket_exception_count != 0
  exceptions["socket_exception_ssl_count"] = socket_exception_ssl_count if socket_exception_ssl_count != 0
  exceptions["socket_exception_timeout_count"] = socket_exception_timeout_count if socket_exception_timeout_count != 0
  if socket_exception_read_timeout_count != 0
    exceptions["socket_exception_read_timeout_count"] = socket_exception_read_timeout_count
  end
  if socket_exception_failed_to_connect_count != 0
    exceptions["socket_exception_failed_to_connect_count"] = socket_exception_failed_to_connect_count
  end
  if socket_exception_count != 0 && socket_exception_java_ssl_exception_count != 0
    exceptions["socket_exception_java_ssl_exception_count"] = socket_exception_java_ssl_exception_count
  end
  exceptions["failed_to_connect_count"] = ftc_count if ftc_count != 0
  exceptions["failed_to_connect_timeout_count"] = ftc_timeout_count if ftc_timeout_count != 0
  exceptions["failed_to_connect_update_app_pid_count"] = ftc_update_app_pid_count if ftc_update_app_pid_count != 0

  if dns_exception_count != 0 || http_exception_count != 0
    data = {}
    data["dns_exception_count"] = dns_exception_count if dns_exception_count != 0
    data["http_exception_count"] = http_exception_count if http_exception_count != 0
    kind = "suppressed-internet-connectivity-failure"
    BrowserStack.logger.info("Pushing #{kind} failures to zombie")
    zombie_push('android', kind, 'exception', '', data, device, session_id)
  end

  BrowserStack.logger.info("Network issues found in device logs: #{exceptions}")

  File.write("/tmp/device_logs_exception-#{device}.json", JSON.dump(exceptions)) unless exceptions.empty?
end

def check_crypto_configuration_error(device, device_name, session_id, user_id, app_bundle_id, device_logs_file)
  # This indicates that an operation was attempted that could not be supported by the crypto system of the device in its
  # current configuration. It may occur when the license policy requires device security features that aren't supported
  # by the device, or due to an internal error in the crypto system that prevents
  # the specified security policy from being met.
  crypto_error_count = `timeout 10 grep "android.media.MediaCodec\\$CryptoException: Operation not supported in this \
configuration" #{device_logs_file} | wc -l`.strip.to_i
  if crypto_error_count > 0
    BrowserStack.logger.info("[crypto_configuration_error] crypto configuration error. count: #{crypto_error_count}")
    zombie_push("android", "crypto-configuration-error", app_bundle_id,
                device_name, crypto_error_count, device, session_id, user_id)
  end
rescue StandardError => e
  BrowserStack.logger.error("[crypto_configuration_error #{session_id}] Exception : #{e.message} #{e.backtrace}")
end

def check_app_main_thread_load(device, device_name, session_id, user_id, app_bundle_id, device_logs_file)
  # Whenever the below log line is seen for the user's app, it is observed that app's certain functionality don't work
  # such as users' custom camera implementation.
  # Sample log line : 12-14 15:14:05.619 I/Choreographer(14776): Skipped 33 frames!
  # The application may be doing too much work on its main thread.
  str = "The application may be doing too much work on its main thread"
  app_main_thread_load_count = `timeout 10 grep '#{str}' #{device_logs_file} | wc -l`.strip.to_i
  if app_main_thread_load_count > 0
    BrowserStack.logger.info(
      "[check_app_main_thread_load #{session_id}] Detected too much load on main thread : "\
      "#{app_main_thread_load_count}"
    )
    zombie_push(
      "android", "app-main-thread-load", app_bundle_id, device_name,
      app_main_thread_load_count, device, session_id, user_id
    )
  end
rescue StandardError => e
  BrowserStack.logger.error("[check_app_main_thread_load #{session_id}] Exception : #{e.message} #{e.backtrace}")
end

def check_play_services_used(device, device_name, session_id, user_id, logcat_file)
  play_services_used = `egrep -o 'https?://play.googleapis.com[^ ]+' #{logcat_file} | sort | uniq | tail -4`
  unless play_services_used.empty?
    BrowserStack.logger.info("[Detected play services used #{session_id}]: #{play_services_used}")
    zombie_push("android", "play-services-used", "Detected play services used #{session_id}",
                device_name, play_services_used, device, session_id, user_id)
  end
end

def check_billing_issues(device, device_name, session_id, user_id, logcat_file)
  cmd = "timeout 10 fgrep #{session_id} #{logcat_file} "\
        " | grep -o 'Unsupported billing API version' | wc -l"
  billing_issues_count = `#{cmd}`.strip
  if billing_issues_count.to_i > 0
    BrowserStack.logger.info("billing-error: Unsupported billing API version for #{session_id}]: device: #{device}")
    zombie_push(
      "android", "billing-error",
      "Detected Unsupported billing API version error for #{session_id}", device_name,
      billing_issues_count, device, session_id, user_id
    )
  end
rescue StandardError => e
  BrowserStack.logger.error("[check_billing_issues #{session_id}] Exception : #{e.message} #{e.backtrace}")
end

def check_device_time_sync_issues(device, device_name, session_id, user_id, logcat_file)
  device_time_sync_issues_count = `grep -o 'Missing NTP fix' #{logcat_file} | wc -l`.strip

  if device_time_sync_issues_count.to_i > 0
    BrowserStack.logger.info("device-time-sync-issue: java.time.DateTimeException: "\
                             "Missing NTP fix #{session_id}]: device: #{device}")
    zombie_push("android", "device-time-sync-issue", "Missing NTP fix", device_name, device_time_sync_issues_count,
                device, session_id, user_id)
  end
rescue StandardError => e
  BrowserStack.logger.error("[check_device_time_sync_issues #{session_id}] Exception : #{e.message} #{e.backtrace}")
end

def check_firebase_logcat_error(device, device_name, session_id, user_id, logcat_file)
  gcm_init_issues_count = `grep -o 'GCM FAILED TO INITIALIZE' #{logcat_file} | wc -l`.strip

  if gcm_init_issues_count.to_i > 0
    BrowserStack.logger.info("firebase-issue: java.time.DateTimeException: Missing NTP fix "\
                             "#{session_id}]: device: #{device}")
    zombie_push("android", "firebase-issue", "GCM FAILED TO INITIALIZE", device_name, gcm_init_issues_count,
                device, session_id, user_id)
  end
rescue StandardError => e
  BrowserStack.logger.error("[check_device_time_sync_problems #{session_id}] Exception : #{e.message} #{e.backtrace}")
end

def check_privoxy_issues(device, session_id, user_id)
  privoxy_log_path = File.join(BrowserStack::LOG_DIR_PATH, "privoxy-#{device}.log")
  return unless File.file?(privoxy_log_path)

  max_connections =
    `grep -i "Connect: Rejecting connection from .* Maximum number of connections reached" #{privoxy_log_path}`
  max_connections = max_connections.split("\n")

  stack_smashing_detected = `grep -i "stack smashing detected" #{privoxy_log_path}`
  stack_smashing_detected = stack_smashing_detected.split("\n")

  if max_connections.count > 0
    zombie_push("android", "privoxy-max-connection", "", "", max_connections.count, device, session_id, user_id)
    event = {}
    event["hashed_id"] = session_id
    event["product"] = { "stability" => { "reason" => "error-network-issues" } }
    push_to_eds(EdsConstants::APP_AUTOMATE_TEST_SESSIONS, event)
  end

  if stack_smashing_detected.count > 0
    zombie_push("android", "privoxy-stack-smashing-detected", "", "",
                stack_smashing_detected.count, device, session_id, user_id)
  end
rescue StandardError => e
  BrowserStack.logger.error("[check_privoxy_issues #{session_id}] Exception : #{e.message} #{e.backtrace}")
end

def push_to_eds(test_sessions_table, event)
  return if event.empty? || event["hashed_id"].nil?

  eds = EDS.new({}, BrowserStack.logger)
  eds.push_logs(test_sessions_table, event)
rescue StandardError => e
  BrowserStack.logger.error("Got #{e.message} #{e.backtrace} when pushing data to EDS")
end

def get_device_network_info(device)
  `#{BrowserStack::ADB} -s #{device} shell 'dumpsys wifi' | grep mNetworkInfo`
end

def is_wifi_down?(device)
  network_info = get_device_network_info(device)
  !network_info.scan(/\w+/).include?("CONNECTED")
end

def get_session_params(device)
  # This function will throw an exception if session file cannot be parsed
  JSON.parse(File.read("#{STATE_FILES_DIR}/session_#{device}"))
end

def version_less_than(operator1, operator2)
  (Gem::Version.new(operator1) < Gem::Version.new(operator2))
end

# To change the default appium version, there is a similar function in:
# android/common.sh#get_default_appium_version
def get_default_appium_version(os_version)
  default_appium_version = File.read(BrowserStack::APPIUM_CONFIG_FILE).strip
  version_less_than(os_version, "5.0") ? "1.6.5" : default_appium_version
end

# TODO: remove this when we move to using appium settings app to mock location for all appium versions
def get_mock_location_app(appium_version)
  if appium_version.nil? || Gem::Version.new(appium_version) > Gem::Version.new('1.9.1')
    'io.appium.settings'
  else
    'com.android.browserstack'
  end
end

def check_privoxy_process(device)
  session_data = get_session_params(device)
  session_id = get_session_id_from_params(session_data)
  session_genre = session_data["genre"]
  errors = []

  # privoxy isn't used with hosts is nil, which means local testing isn't in use
  return if session_data["hosts"].nil? || session_data["hosts"].empty?

  process_list = `ps aux`.split("\n")
  errors.push("Privoxy not running") unless process_list.any? do |process|
    process.include?("#{device}.txt") && !process.include?("launch_privoxy")
  end
  errors.push("Launch_privoxy not running") unless process_list.any? do |process|
    process.include?("#{device}.txt") && process.include?("launch_privoxy")
  end

  errors.each do |error|
    BrowserStack.logger.error("#{error} for #{device}")
  end

  unless errors.empty?
    zombie_push("android", "privoxy_process_check_failed", errors.join(','), "",
                session_genre, device, session_id, is_app_accessibility: params[:is_app_accessibility])
  end
end

def get_timezone(device)
  adb(device).getprop("persist.sys.timezone")
end

def get_orientation_from_settings(device)
  # 0 & 2 are portrait and 1 & 3 are landscape
  # https://developer.android.com/reference/android/view/Surface#ROTATION_0
  rotation = `#{BrowserStack::ADB} -s #{device} shell settings get system user_rotation`.strip.to_i
  rotation.even? ? "portrait" : "landscape"
end

def validate_speedlab_network_profile(network_profile)
  network_profile.key?("network_bw_dwld") &&
  network_profile.key?("network_bw_upld") &&
  network_profile.key?("network_latency") &&
  network_profile.key?("network_pk_loss")
end

def get_bundle_ids_from_session_info(session_info)
  app_bundle_ids = session_info["packages"]
  app_bundle_ids.map! { |package| package["name"] }
  app_bundle_ids.join("/")
end

def adb_file_cmd(action, device_id, source, dest, opts = {})
  tag = opts[:tag] || 'GENERIC'
  timeout = opts[:timeout] || 20
  cmd = ["timeout", timeout.to_s, BrowserStack::ADB.to_s, "-s", device_id.to_s, action.to_s, source.to_s, dest.to_s]

  output, cmd_status = Open3.capture2(*cmd)
  exit_code = cmd_status.exitstatus # 0 -> Success, 124 -> Timeout, 127 -> Error
  BrowserStack.logger.info "[#{tag}: adb_#{action}_cmd] command - #{cmd.inspect} | "\
                           "exit code - #{exit_code} | output - #{output}"

  [exit_code, output]
end

def adb_shell_cmd(action, device_id, device_file_path, opts = {})
  tag = opts[:tag] || 'GENERIC'
  redirect_err_to_out = opts[:redirect_err_to_out] || false

  cmd = "timeout 20 #{BrowserStack::ADB} -s #{device_id} shell #{action} \"#{device_file_path}\""
  cmd += " 2>&1" if redirect_err_to_out

  output = `#{cmd}`
  exit_code = $CHILD_STATUS.exitstatus
  BrowserStack.logger.info "[#{tag}: adb_#{action}_cmd] command - #{cmd} | "\
                           "exit code - #{exit_code} | output - #{output}"

  [exit_code, output]
end

def add_key_to_duplicate_session_summary(device, key, value)
  file_path = "/tmp/duplicate_session_#{device}"
  sess_details = JSON.parse(File.read(file_path))
  sess_details[key] = value
  File.open(file_path, 'w') { |f| f.write(sess_details.to_json) }
end

def get_key_from_duplicate_session_summary(device, key)
  file_path = "/tmp/duplicate_session_#{device}"
  sess_details = JSON.parse(File.read(file_path))
  sess_details[key]
end

def process_running?(pid)
  Process.getpgid(pid)
  true
rescue Errno::ESRCH
  false
end

def parse_app_install_output(output, exit_status_code, is_apks_archive = false) # rubocop:todo Style/OptionalBooleanParameter
  if is_apks_archive
    regex_pattern = /IncompatibleDeviceException.*/
    match_data = output.match(regex_pattern)
    default_output = "APKS Installation failed with unknown reason with exit_status_code #{exit_status_code}" + output
  else
    regex_pattern = /\[INSTALL.*FAILED.*\]/
    match_data = output.match(regex_pattern)
    default_output = "Installation failed with unknown reason with exit_status_code #{exit_status_code}" + output
  end
  match_data ? match_data[match_data.size - 1] : default_output
end

def check_keystore_issues(device, device_name, session_id, user_id, logcat_file)
  cmd = "cat #{logcat_file} "\
        " | timeout 15 sed -n -E -e 's|.*E\.*android.hardware.keymaster[^:]*\: |\1|p' "\
        " | sort | uniq -c"
  keymaster_errors = `#{cmd}`.to_s
  keymaster_errors.delete! "\u0001"
  data = {}
  vector = keymaster_errors.split(/(.*?)(\d.*?)/)

  keymaster_errors.split("\n").each do |line|
    splitted = line.split(' ', 2)
    data[splitted[1].to_s] = splitted[0].to_s
  end

  unless data.empty?
    BrowserStack.logger.info("[Detected Keystore errors #{session_id}]: #{data}")
    zombie_push("android", "keystore-errors", "exception", device_name, data, device, session_id, user_id)
  end
end

def get_ui_automator_server_port(selenium_port)
  8200 + selenium_port.to_i - 38080
end

def start_accessibility_service(params, device_id, session_id, genre, logger)
  mark_event_start('enable_watcher', params[:event_hash]) if params[:event_hash]
  begin
    browserstack_watcher_manager = WatcherHelper.new(device_id, session_id, genre, logger)
    # Talkback is another accessibility service used by L/AL in-session, adding reference for future changes
    # https://github.com/browserstack/mobile/blob/master/android/lib/talkback_helper.rb#L17
    browserstack_watcher_manager.start_accessibility_service
    # sleep to wait for the watcher to get registered as the accessibility service.
    # Without this, the genre won't get set correctly.
    sleep 0.1
    browserstack_watcher_manager.set_genre
    browserstack_watcher_manager.start_network_monitor
  rescue StandardError => e
    logger.error("[BROWSERSTACK_WATCHER] Failed to start accessibility service: #{e.message} "\
                 "backtrace: #{e.backtrace.join('\n')}.")
  end
  mark_event_end('enable_watcher', params[:event_hash]) if params[:event_hash]
end

# Creates an archive and truncates the given log file
# Params:
# +log_file_path+:: path of the log file to truncate
def truncate_log_file(log_file_path)
  return unless File.exist?(log_file_path)

  log_to_append = File.read(log_file_path)
  BrowserStack.logger.info("truncate_log_file: Starting archival of #{log_file_path} to #{log_file_path}.archive ")
  File.open("#{log_file_path}.archive", 'a') do |archive_log_file|
    archive_log_file.puts log_to_append
  end
  BrowserStack.logger.info("truncate_log_file: Truncating #{log_file_path}")
  File.truncate(log_file_path, 0)
end

def download_and_install_qa_cleanup_test_apks(response, adb)
  # 1. copy main+test app from testci to host machine
  BrowserStack::HttpUtils.download_file(CLEANUP_APP, '/tmp/cleanup.apk', "app", { retry_count: 3, timeout: 500 })
  BrowserStack::HttpUtils.download_file(
    CLEANUP_TEST_APP, '/tmp/cleanupTests.apk', "app", { retry_count: 3, timeout: 500 }
  )
  # 2. install both apps on device
  response['install_cleanup.apk'] = adb.install("/tmp/cleanup.apk", '-r', '-g', '-t', '-d', timeout: 500)
  response['install_cleanupTests.apk'] = adb.install("/tmp/cleanupTests.apk", '-r', '-g', '-t', '-d', timeout: 500)
  response
end

def notify_pusher_live(message, params, other_parms =nil)
  # Use for Live sessions only
  pusher_params_hash = {
    type: "live_dashboard",
    live_session_id: params["session_id"],
    channel: params["pusher_channel"],
    token: params["pusher_auth"],
    event: "logs",
    message: message
  }
  pusher_params_hash.merge!(other_parms) unless other_parms.nil?
  pusher_params = URI.encode_www_form(pusher_params_hash)
  pusher_url = "#{params['pusher_url']}/sendMessage"
  BrowserStack.logger.info "Sending #{message} to #{pusher_url} with params #{pusher_params}}"
  OSUtils.execute(%(curl -XPOST "#{pusher_url}" -d "#{pusher_params}"), timeout: 60)
rescue StandardError => e
  BrowserStack.logger.error "Sending message to pusher failed #{e.message}"

end

def notify_pusher_app_live(message, params , other_parms =nil)
  # Use for AppLive sessions only
  pusher_params_hash = {
    type: "app_live_dashboard",
    app_live_session_id: params["session_id"],
    channel: params["pusher_channel"],
    token: params["pusher_auth"],
    event: "logs",
    message: message
  }
  pusher_params_hash.merge!(other_parms) unless other_parms.nil?
  pusher_params = URI.encode_www_form(pusher_params_hash)
  pusher_url = "#{params['pusher_url']}/sendMessage"
  BrowserStack.logger.info "Sending #{message} to #{pusher_url} with params #{pusher_params}}"
  OSUtils.execute("curl -XPOST \"#{pusher_url}\" -d \"#{pusher_params}\"")
rescue StandardError => e
  BrowserStack.logger.error "Sending message to pusher failed #{e.message}"

end

def notify_pusher(message, params, other_params, product)
  if product == BrowserStack::LIVE
    notify_pusher_live(message, params, other_params)
    return
  end
  notify_pusher_app_live(message, params, other_params)
end

def run_qa_cleanup_tests(response, adb, device, suite_type, subsuite_type, record_video, device_name)
  # 3. unlock screen
  `#{SCRIPT} ensure_screen_is_unlocked #{device}`
  # 4. increase screen_lock_timeout
  adb.shell('settings put system screen_off_timeout 86400000')
  # 5. start video recording
  if record_video.eql? 'true'
    `sudo rm -rf /tmp/#{device}`
    begin
      Process.fork { record_video_for_cleanup_suite(adb, device, record_video, device_name) }
    rescue StandardError => e
      raise e
    end
  end
  # 6. start test
  test_suite_cmd = "am instrument -w -e suiteType #{suite_type} -e subSuiteType #{subsuite_type} "
  test_suite_cmd += "-e debug false -e device #{device_name} "
  test_suite_cmd += 'com.example.mytestapplication.test/androidx.test.runner.AndroidJUnitRunner'
  response["test_op"] = adb.shell(test_suite_cmd, timeout: 500)
  response
end

def record_video_for_cleanup_suite(adb, device, record_video, device_name)
  adb.shell('rm -rf /sdcard/videos')
  adb.shell('mkdir -p /sdcard/videos')
  max_recording_count = 4
  i = 0
  FileUtils.mkdir_p("/tmp/#{device}")
  FileUtils.mkdir_p("/tmp/#{device}/videos")
  FileUtils.touch("/tmp/#{device}/videos/videos.txt")
  while i < max_recording_count
    if record_video.eql? 'true'
      cmd = if device_name.include?('Oppo') || device_name.include?('Realme') || device_name.include?('OnePlus')
              "adb -s #{device} shell '/data/local/tmp/screenrecord --verbose "\
              " --time-limit 180 --size 800x800 --bit-rate 1500000 "\
              " /sdcard/videos/video_#{i}.mp4' > /dev/null &"
            else
              "adb -s #{device} shell "\
              " 'screenrecord --verbose "\
              " --time-limit 180 --size 800x800 --bit-rate 1500000 "\
              " /sdcard/videos/video_#{i}.mp4' > /dev/null &"
            end
      exec(cmd)
    end
    sleep 180
    i += 1
  end
end

def fetch_and_upload_test_video(response, adb, device, _record_video, session_id)
  # 7. kill screenrecord process
  `ps aux | grep screenrecord | grep #{device} | awk '{print $2}' | xargs kill -15`
  sleep 5
  `ps aux | grep screenrecord | grep #{device} | awk '{print $2}' | xargs kill -9`
  # 8. get the video from device to host
  response["pull_cmd"] = adb.pull("/sdcard/videos", "/tmp/#{device}/", timeout: 6000)
  FileUtils.cd("/tmp/#{device}/videos")
  `for f in video_*.mp4; do echo "file '$f'" >> videos.txt; done`
  `sudo rm -rf /tmp/#{device}.mp4`
  `#{BrowserStack::FFMPEG} -f concat -i videos.txt -c copy /tmp/#{device}.mp4`
  # 9. upload video to s3
  client = Aws::S3::Client.new(profile: 'bs')
  s3_url = "#{S3_BASE_URL}/mobile_regression/#{device}/#{session_id}.mp4"
  response["s3_url"] = s3_url
  regex_result = s3_url.match(%r{.com/([a-zA-Z0-9-]+)/(.*)$}i).captures
  key_path = regex_result[1]
  bucket = regex_result[0]
  File.open("/tmp/#{device}.mp4", 'rb') do |file|
    response['s3_upload'] = client.put_object({
      acl: 'public-read',
      body: file,
      bucket: bucket,
      key: key_path,
      content_type: 'video/mp4'
    })
  end
  `sudo rm -rf /tmp/#{device}.mp4`
  response
end

def is_port_open?(ip, port, timeout=2)
  connect_to(ip, port, timeout).close
  true
rescue StandardError => e
  false
end

def instrument_device_off_adb(output, device, session_id, source, status)
  return unless output&.match("device .* not found|device offline|device unauthorized")

  data = { "status" => status, "source" => source }
  zombie_push("android",
              "device-off-adb",
              output,
              "",
              data.to_json,
              device,
              session_id)
end

def is_device_off_adb?(device_id, test_id)
  return true if File.exist?("#{BrowserStack::ESPRESSO_DEVICE_OFF_ADB_FILE_PREFIX}_#{test_id}_#{device_id}")

  cmd = "adb -s #{device_id} shell date 2>&1"
  output, status = OSUtils.execute(cmd, true)
  true if output&.match("device .* not found|device offline|device unauthorized")
end

def safe_file_write(file_path, device_id, session_id, source, &block)
  # rubocop:disable Lint::RescueException
  File.open(file_path, "w+", &block)
rescue Exception => e
  BrowserStack.logger.info "#{session_id} Error while writing to file. Sending error to pager"
  zombie_push("android",
              "file-write-error",
              e.message + e.backtrace.join("\n"),
              "",
              { source: source, file: file_path }.to_json,
              device_id,
              session_id)
  # rubocop:enable Lint::RescueException
end

def connect_to(host, port, timeout=nil)
  addr = Socket.getaddrinfo(host, nil)
  sock = Socket.new(Socket.const_get(addr[0][0]), Socket::SOCK_STREAM, 0)

  if timeout
    secs = Integer(timeout)
    usecs = Integer((timeout - secs) * 1_000_000)
    optval = [secs, usecs].pack("l_2")
    sock.setsockopt(Socket::SOL_SOCKET, Socket::SO_RCVTIMEO, optval)
    sock.setsockopt(Socket::SOL_SOCKET, Socket::SO_SNDTIMEO, optval)
  end
  sock.connect(Socket.pack_sockaddr_in(port, addr[0][3]))
  sock
end

# rubocop:disable Layout/LineLength
def kill_ai_proxy(ai_proxy_port, terminal_ip, retries = 3)
  while retries >= 0
    ai_proxy_pids = `lsof -i -P -n | grep node | grep #{ai_proxy_port} | grep #{terminal_ip}:#{ai_proxy_port} | awk '{print $2}'`
    if !ai_proxy_pids.nil? && ai_proxy_pids != ""
      pids = ai_proxy_pids.split("\n")
      BrowserStack.logger.info "Killing ai_proxy founds Pids to kill #{pids} running on port #{ai_proxy_port}"
      pids.each do |pid|
        system("kill -9 #{pid}")
      end
    else
      break
    end
    retries -= 1
  end
end
# rubocop:enable Layout/LineLength

def set_ai_proxy_config(ai_proxy_config_path, terminal_ip, selenium_port, ai_proxy_upstream,
                        ai_proxy_port, device, device_name, device_version, product, request_params)
  begin
    file_data = File.read(ai_proxy_config_path)
    json_data = JSON.parse(file_data)
  rescue StandardError
    file_data = File.read("#{ai_proxy_config_path}.sample")
    json_data = JSON.parse(file_data)
  end

  json_data["prod"]["hostname"] = terminal_ip
  json_data["prod"]["upstream"] = ai_proxy_upstream if ai_proxy_upstream
  json_data["prod"]["upstreamPort"] = selenium_port.to_i if selenium_port
  json_data["prod"]["port"] = ai_proxy_port if ai_proxy_port
  json_data["prod"]["deviceId"] = device if device
  json_data["prod"]["product"] = product if product
  json_data["prod"]["deviceName"] = device_name if device_name
  json_data["prod"]["deviceVersion"] = device_version if device_version

  if request_params['ai_enabled_session']['keyObject']
    json_data["prod"]["keyObject"] = JSON.parse(request_params['ai_enabled_session'])['keyObject']
  end

  if request_params['ai_enabled_session']['tcgService']
    json_data["prod"]["tcgService"] = JSON.parse(request_params['ai_enabled_session'])['tcgService']
  end

  if request_params['ai_enabled_session']['tcgUrl']
    json_data["prod"]["tcgUrl"] = JSON.parse(request_params['ai_enabled_session'])['tcgUrl']
  end

  @static_conf = StaticConf::StaticConfHelper.setup
  if @static_conf
    json_data["prod"]["edsConfig"] = {
      "zombie_server": STATIC_CONF['zombie_url'],
      "zombie_port": 8553,
      "eds_server": request_params[:edsHost] || STATIC_CONF["eds_host"],
      "eds_port": request_params[:edsPort] || STATIC_CONF["eds_port"],
      "eds_key": request_params[:edsKey] || STATIC_CONF["eds_api_key"]
    }
  end

  File.open(ai_proxy_config_path, "w") do |f|
    f.write(json_data.to_json)
  end
  json_data
end

def kill_cdp_proxy(cdp_port, terminal_ip)
  cdp_pids = `lsof -i -P -n | grep node | grep #{cdp_port} | grep #{terminal_ip}:#{cdp_port} | awk '{print $2}'`
  # cdp_pids = `lsof -t -i :#{cdp_port}
  if !cdp_pids.nil? && cdp_pids != ""
    pids = cdp_pids.split("\n")
    BrowserStack.logger.info "Killing cdp_proxy founds Pids to kill #{pids} running on port #{cdp_port}"
    pids.each do |pid|
      system("kill -9 #{pid}")
    end
  end
end

def kill_playwright_server(playwright_port)
  playwright_pid = `lsof -t -i :#{playwright_port}`.strip

  if !playwright_pid.nil? && playwright_pid != ""
    pids = playwright_pid.split("\n")
    pids.each do |pid|
      playwright_process  = `ps aux | grep #{pid} | grep -v grep | grep playwright_port`.strip
      next unless !playwright_process.nil? && playwright_process != ""

      BrowserStack.logger.info(
        "[stop] stopping playwright server with pid : #{pid} & process: #{playwright_process} " \
         "running on port #{playwright_port}"
      )
      system("kill -9 #{pid}")
      data = { "pid" => pid, "playwright_process" => playwright_process }
      zombie_push("android", "playwright-process-killed", "", "", data.to_json)
    end
  end
end

def read_file(file_path)
  return nil unless File.exist?(file_path)

  File.open(file_path, &:readline)
rescue Errno::ENOENT => e
  BrowserStack.logger.info "File not found at path #{file_path}"
  nil
end

def hash_inspect_to_hash(string)
  # order is important so pls don't re-arrange it.
  # remove any backslashes from the input string
  sanitized_string = string.gsub(/\\/, '')
  # Replace nil with null
  sanitized_string.gsub!(/=>\s*nil/, ': null')
  # looks for a pattern like `"KEY" =>` and replaces it with `"KEY":`
  sanitized_string.gsub!(/"(\w+)"\s*=>/, '"\1":')

  JSON.parse(sanitized_string)
rescue StandardError => e
  BrowserStack.logger.info BrowserStack.logger.info "Not able to parse string to json error: #{redact_percy_token(
    e.message, 'PERCY_TOKEN'
  )}"
  raise e
end

def redact_percy_token(string, token)
  regex = /("#{token}"\s*=>\s*")[^"]+(")/
  string.gsub(regex, '\1[REDACTED]\2')
end

def zip_logs(path_to_file, file_size_bytes)
  compression_start_time = DateTime.now.strftime('%Q').to_i
  path_to_zipped_file = "#{path_to_file}.gz"
  updated_path_to_file = path_to_file
  zipped_file_size_kb = -1
  zipped_file_size_bytes = -1

  begin
    res = system('gzip', '-1', '-k', path_to_file)
    BrowserStack.logger.info "Zip command returned exit code #{res}"

    if res.to_s == 'true' && File.exist?(path_to_zipped_file)
      updated_path_to_file = path_to_zipped_file
      if File.file?(updated_path_to_file)
        zipped_file_size_bytes = begin
          File.stat(updated_path_to_file).size.to_i
        rescue StandardError => e
          BrowserStack.logger.info "Error occurred in getting zipped file size #{updated_path_to_file}: #{e.message}"
          -1
        end
        zipped_file_size_kb = zipped_file_size_bytes / 1024
      end
      BrowserStack.logger.info "Compressed #{path_to_file} of size (in bytes) #{file_size_bytes}"
      BrowserStack.logger.info "Compressed File Path: #{path_to_zipped_file} Size (in bytes): #{zipped_file_size_bytes}"
    end
  rescue StandardError => e
    BrowserStack.logger.info "Error occurred while zipping the logs: #{e.message}"
  end
  compression_time_ms = DateTime.now.strftime('%Q').to_i - compression_start_time
  [updated_path_to_file, zipped_file_size_kb, zipped_file_size_bytes, compression_time_ms]
end

def delete_file(file_path)
  if File.exist?(file_path)
    File.delete(file_path)
    BrowserStack.logger.info("Deleted file with path #{file_path}")
  end
rescue StandardError => e
  BrowserStack.logger.error("Exception while deleting file #{file_path} reason #{e.message}")
end

def exec_block(using_new_thread, &block)
  raise "exec_block: no block given" unless block_given?

  new_thread = nil
  if using_new_thread
    new_thread = Thread.bs_run do
      block.call
    end
  else
    block.call
  end

  new_thread
end

def get_stream_size_for_device_state(device_name, device_state)
  interaction_json = read_interaction_json
  device_params = interaction_json[device_name]

  if device_state == "3"
    height = device_params["streaming_height_state_3"]
    width = device_params["streaming_width_state_3"]
  else
    height = device_params["streaming_height"]
    width = device_params["streaming_width"]
  end

  { height: height, width: width }
end

def get_screen_size_for_interactions(device_name)
  interaction_json = read_interaction_json
  interaction_params = interaction_json[device_name]
  "#{interaction_params['max_width']} #{interaction_params['max_height']}"
end

def get_interactions_server_pids(device_id)
  adb_output = adb(device_id).shell("ps -ef | grep -E 'interactionsserver|interactions_server' | grep -v grep")
  `echo #{adb_output} | awk '{ print $2 }' | tr '\n' ' '`
end

def restart_interactions_server(device_id, device_name)
  # kill interactions server
  begin
    interactions_server_pids = get_interactions_server_pids(device_id)
    adb(device_id).shell("kill #{interactions_server_pids}")
  rescue StandardError => e
    BrowserStack.logger.info("Failed in killing interaction server #{e.message}")
  end

  # start interactions server
  touch_screen_size = get_screen_size_for_interactions(device_name)
  interactions_server_command = "CLASSPATH=/data/local/tmp/interactions-server-" \
    "#{BrowserStack::INTERACTIONS_SERVER_VERSION} app_process / com.browserstack.interactionsserver.Server " \
    "#{touch_screen_size} &"
  cmd = "adb -s #{device_id} shell \"#{interactions_server_command}\""
  spawn_process(cmd)
end

def get_input_injector_pids(device_id)
  adb_output = adb(device_id).shell("ps -ef | grep -E 'inputinjector' | grep -v grep", timeout: 3, retries: 1)
  `echo #{adb_output} | awk '{ print $2 }' | tr '\n' ' '`
end

def restart_input_injector(device_id)
  # kill input injector
  begin
    input_injector_pids = get_input_injector_pids(device_id)
    adb(device_id).shell("kill -9 #{input_injector_pids}", timeout: 3)
  rescue StandardError => e
    BrowserStack.logger.info("Failed in killing input injector #{e.message}")
  end

  # start input injector
  cmd = "timeout 5 adb -s #{device_id.shellescape} shell 'CLASSPATH=\"/data/local/tmp/InputInjector.apk\" app_process" \
  " / com.browserstack.inputinjector.Main &' &"
  system(cmd)
end

def get_device_state(device_id)
  adb(device_id).shell("cmd device_state print-state")
end

def unlock_device_screen(device_id)
  adb(device_id).shell("input keyevent 82")
end

def update_device_state(device_id, device_state)
  adb(device_id).shell("cmd device_state state #{device_state}")
end

def update_screen_resolution(device_id, device_state, device_name)
  new_stream_size = get_stream_size_for_device_state(device_name, device_state)
  height = new_stream_size[:height]
  width = new_stream_size[:width]

  extra_string = "--es action \"update-resolution\" --es width #{width} --es height #{height}"
  adb(device_id).shell("am startservice --user 0 -n fr.pchab.AndroidRTC2/.RTCService #{extra_string}")
end

def toggle_device_screen(device_id, device_state, device_name)
  update_screen_resolution(device_id, device_state, device_name)
  update_device_state(device_id, device_state)
  unlock_device_screen(device_id) if device_state == "0"
  restart_interactions_server(device_id, device_name)
end

def download_and_install_qa_ui_automation_test(adb)
  # 1. copy main+test app from testci to host machine
  BrowserStack::HttpUtils.download_file(
    QA_UI_AUTOMATOR_APP, QA_TEMP_PATH_APP , "app", { retry_count: 3, timeout: 500 }
  )
  BrowserStack::HttpUtils.download_file(
    QA_UI_AUTOMATOR_TEST, QA_TEMP_PATH_TEST_SUITE , "app", { retry_count: 3, timeout: 500 }
  )
  # 2. install both apps on device
  adb.install(QA_TEMP_PATH_APP, '-r', '-g', '-t', '-d', timeout: 500)
  adb.install(QA_TEMP_PATH_TEST_SUITE, '-r', '-g', '-t', '-d', timeout: 500)
end

def uses_chrome_v114(model, version, device_id, dedicated_device)
  if dedicated_device
    begin
      if File.file?(BrowserStack::PRIVATE_CLOUD_CHROME_OVERRIDE_DEVICES_FILE)
        chrome_overrides_json = JSON.parse(File.read(BrowserStack::PRIVATE_CLOUD_CHROME_OVERRIDE_DEVICES_FILE))
        return true if chrome_overrides_json["v114"]&.include?(device_id)
      end
    rescue JSON::ParserError => e
      BrowserStack.logger.error("Failed to parse PRIVATE_CLOUD_CHROME_OVERRIDE_DEVICES_FILE: #{e.message}")
    rescue StandardError => e
      BrowserStack.logger.error("Error reading PRIVATE_CLOUD_CHROME_OVERRIDE_DEVICES_FILE: #{e.message}")
    end
  end

  devices = [
    "SM-S908B=12",
    "SM-S908E=12",
    "SM-G991B=12"
  ]

  devices.each do |device|
    device_model, device_version = device.split("=")

    return true if device_model == model && device_version.to_i == version
  end

  false
end

def uses_chrome_v120_v609914431(model, version)
  devices = [
    "SM-S906B=12",
    "SM-S906E=12",
    "SM-X706B=12",
    "Pixel 6=12",
    "Pixel 6 Pro=12",
    "SM-S901E=12",
    "SM-S901B=12",
    "Pixel 5=11",
    "Pixel 5=12"
  ]

  devices.each do |device|
    device_model, device_version = device.split("=")

    return true if device_model == model && device_version.to_i == version
  end

  false
end

def uses_chrome_v123_v631204031(model, version)
  devices = [
    "SM-S901E=12",
    "SM-S901B=12",
    "SM-X706B=12",
    "SM-S906B=12",
    "SM-S906E=12",
    "Pixel 6=12",
    "Pixel 6 Pro=12",
    "Pixel 5=11",
    "Pixel 5=12",
    "SM-T875=11",
    "SM-T876B=11",
    "SM-T870=11"
  ]

  devices.each do |device|
    device_model, device_version = device.split("=")

    return true if device_model == model && device_version.to_i == version
  end

  false
end

def update_insecure_ws_device_config(session_id, device_ip, repeater_host,
                                     repeater_port, privoxy_port, _terminal_ip,
                                     insecure_ws_proxy_params_json, should_remove)
  # returns true if after deleting this device, config is empty. That will lead to killing of the proxy service

  if device_ip.nil? || device_ip.empty?
    BrowserStack.logger.error "No device IP found while trying to update device config for Insecure WS Proxy!"
    return false
  end

  if should_remove && !File.exist?(INSECURE_WS_PROXY_DEVICES_CONF)
    BrowserStack.logger.error `Cannot delete device_ip : #{device_ip} from config for
                                Insecure WS Proxy as it does not exist`
    return true
  end

  FileUtils.touch(INSECURE_WS_PROXY_DEVICES_CONF) unless File.exist?(INSECURE_WS_PROXY_DEVICES_CONF)

  return_val = false

  File.open(INSECURE_WS_PROXY_DEVICES_CONF, 'r+') do |file|
    file.flock(File::LOCK_EX)

    existing_data = if File.size?(INSECURE_WS_PROXY_DEVICES_CONF)
                      JSON.parse(File.read(INSECURE_WS_PROXY_DEVICES_CONF))
                    else
                      {}
                    end

    new_device_mapping = if !should_remove
                           {
                             "session_id" => session_id.to_s,
                             "repeater_host" => repeater_host.to_s,
                             "repeater_port" => repeater_port.to_s,
                             "privoxy_port" => privoxy_port.to_s,
                             "insecure_ws_proxy_params" => insecure_ws_proxy_params_json
                           }
                         else
                           {}
                         end

    if should_remove
      device_ip.each do |ip|
        existing_data.delete(ip.to_s)
      end
      return_val = true if existing_data.empty?
    else
      device_ip.each do |ip|
        existing_data[ip.to_s] = new_device_mapping
      end
      return_val = true
    end

    json_data = JSON.pretty_generate(existing_data)
    file.rewind
    file.truncate(0)
    file.write(json_data)
    file.flock(File::LOCK_UN)
  end

  BrowserStack.logger.info `update_insecure_ws_device_config done, device_ip : #{device_ip},
                            session_id : #{session_id}, #{should_remove}`
  return_val
rescue StandardError => e
  BrowserStack.logger.info `Some error occurred in update_insecure_ws_device_config method.
                            Exception details: #{e.message}`
  zombie_push('android', 'InsecureWSProxy-update-exception', e.message, '', '', '', session_id)
  false
end

def start_insecure_ws_proxy_server(session_id, repeater_host, repeater_port, privoxy_port,
                                   terminal_ip, insecure_ws_proxy_params, device_ip)
  return nil if repeater_host.nil? || repeater_port.nil?

  unless update_insecure_ws_device_config(session_id, device_ip, repeater_host, repeater_port, privoxy_port,
                                          terminal_ip, JSON.parse(insecure_ws_proxy_params), false)
    BrowserStack.logger.error "Unable to update device config for Insecure WS Proxy."
    zombie_push('android', 'InsecureWSProxy-start-exception', 'Unable to update device config for Insecure WS Proxy.',
                '', '', '', session_id)
    return nil
  end

  # Check if server is already running
  if is_port_open?('127.0.0.1', INSECURE_WS_SERVER_CONFIG['port'])
    BrowserStack.logger.warn "Insecure WS Server is already running!"
    return INSECURE_WS_SERVER_CONFIG['port']
  end

  server_log_path = "#{INSECURE_WS_SERVER_CONFIG['log_path']}.log"
  node_command_env = "INSECURE_WS_SERVER_PORT=#{INSECURE_WS_SERVER_CONFIG['port']} TERMINAL_IP=#{terminal_ip}"
  node_script_command = "#{INSECURE_WEBSOCKET_PROXY} > #{server_log_path} 2>&1"
  command_args = [
    "nice",
    "-n",
    "1",
    "sh",
    "-c",
    "\"#{node_command_env} #{node_script_command}\""
  ]

  command_to_run = command_args.join(' ').to_s

  BrowserStack.logger.info "Starting insecure WS server with command : #{command_to_run} "
  spawn_with_reaper(command_to_run)
  INSECURE_WS_SERVER_CONFIG['port']
rescue StandardError => e
  BrowserStack.logger.info `Some error occurred in start_insecure_ws_proxy_server method.
                            Exception details: #{e.message}`
  zombie_push('android', 'InsecureWSProxy-start-exception', e.message, '', '', '', session_id)
  nil
end

def stop_insecure_ws_proxy_server(device_ip)
  return unless update_insecure_ws_device_config(nil, device_ip, nil, nil, nil, nil, nil, true)

  cmd = "ps -ax | grep 'insecure-websocket-proxy' | awk '{print $1}' | sudo xargs kill -9"
  BrowserStack.logger.info "Stopping insecure WS Server..."
  system(cmd)
rescue StandardError => e
  BrowserStack.logger.info `Some error occurred in stop_insecure_ws_proxy_server method.
                            Exception details: #{e.message}`
  zombie_push('android', 'InsecureWSProxy-stop-exception', e.message, '', '')
end

def get_rule_for_sim_step(device_id, step)
  sim_rules = JSON[File.read(BrowserStack::ANDROID_SIM_RULES_CONFIG)]
  device_model = adb(device_id).model
  os_version = adb(device_id).os_version
  step_config = sim_rules[step]
  rules = step_config['rules'] || []
  via = nil
  intent = nil
  blocking_rules = nil

  # Iterate from bottom to top (reverse order)
  rules.reverse_each do |rule|
    BrowserStack.logger.info "Verifying rule: #{rule}"
    # Check if device and OS version match
    next unless rule['device_model'].include?(device_model) && Regexp.new(rule['os_version']).match?(os_version.to_s)

    via = rule['via']
    intent = rule['intent']
    blocking_rules = rule['blocking_rules'] || []
    break
  end

  via ||= step_config['default_via']
  intent ||= step_config['default_intent']
  blocking_rules ||= step_config['default_blocking_rules'] || []
  [via, intent, blocking_rules]
end

def get_content_type_from_ext(ext)
  case ext
  when 'json'
    'text/json'
  when 'ec'
    'application/octet-stream'
  else
    'text/plain'
  end
end

def is_true?(value)
  !value.nil? && value != "" && value.to_s.downcase == "true"
end

def is_false?(value)
  value == false || value.to_s.downcase == "false"
end

def read_json_file(filename)
  JSON.parse(File.read(filename))
end

def instrument_automation(automation_name, output, device_id)
  BrowserStack.logger.info "Output for #{automation_name}: #{output}"
  if output.include?("OK (1 test)")
    BrowserStack.logger.info "Successfully ran #{automation_name}"
    zombie_push('android', 'android-cleanup-uiautomation', ' ', ' ', "#{automation_name} succeeded", device_id)
  else
    BrowserStack.logger.info "Failed to run #{automation_name}"
    zombie_push('android', 'android-cleanup-uiautomation', ' ', ' ', "#{automation_name} failed", device_id)
  end
end
