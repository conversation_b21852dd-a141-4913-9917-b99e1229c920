require 'open3'
require 'fileutils'
require 'browserstack_logger'
require_relative 'fullcone_nat'

# Singleton factory for managing FullconeNat instances
class FullconeNATManager
  @instance = nil  # Singleton instance

  # Singleton accessor - returns the single instance
  def self.instance
    @instance ||= new
  end

  def initialize
    @full_cone_nat_services = {}
  end

  def get_or_create(tun_interface_ip)
    if @full_cone_nat_services[tun_interface_ip]
      BrowserStack.logger.info("Retrieved existing FullconeNat instance for tun_interface_ip: #{tun_interface_ip}")
      return @full_cone_nat_services[tun_interface_ip]
    end

    BrowserStack.logger.info("Creating new FullconeNat instance for tun_interface_ip: #{tun_interface_ip}")
    @full_cone_nat_services[tun_interface_ip] = FullconeNat.new(tun_interface_ip)
  end

  # Prevent direct instantiation - enforces singleton pattern
  private_class_method :new
end
