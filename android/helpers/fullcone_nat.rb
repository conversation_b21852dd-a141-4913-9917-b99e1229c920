require 'open3'
require 'set'
require 'browserstack_logger'

class FullconeNat
  attr_reader :tun_interface_ip

  def initialize(tun_interface_ip)
    @tun_interface_ip = tun_interface_ip
  end

  def start
    return false if running?

    BrowserStack.logger.info("Starting FullconeNat for tun interface ip: #{@tun_interface_ip}")
    bundle = "/home/<USER>/bin/bundle"

    pid = fork do
      BrowserStack.logger.info("Starting fullcone script for #{@tun_interface_ip}")
      exec("sudo", bundle, "exec", "ruby", "#{__dir__}/fullcone.rb", @tun_interface_ip)
    end

    # Create state file with PID for cross-process tracking
    state_file_path = state_file
    begin
      File.write(state_file_path, pid.to_s)
      BrowserStack.logger.info("Created FullconeNat state file: #{state_file_path} with PID: #{pid}")
    rescue StandardError => e
      BrowserStack.logger.error("Failed to create FullconeNat state file: #{e.message}")
      return false
    end

    Process.detach(pid)
    true
  end

  def stop
    return false unless running?

    BrowserStack.logger.info("Stopping FullconeNat...")

    # Get PID from state file
    state_file_path = state_file
    pid = nil
    begin
      pid = File.read(state_file_path).strip.to_i if File.exist?(state_file_path)
    rescue StandardError => e
      BrowserStack.logger.error("Failed to read PID from state file: #{e.message}")
    end

    if pid && pid > 0
      BrowserStack.logger.info("killing process #{pid} and its children with sudo...")
      system("sudo", "pkill", "-KILL", "-P", pid.to_s)  # Kill children first
      system("sudo", "kill", "-9", pid.to_s)            # Kill parent sudo process
    end

    cleanup_dnat_rules

    begin
      File.delete(state_file_path) if File.exist?(state_file_path)
      BrowserStack.logger.info("Removed FullconeNat state file: #{state_file_path}")
    rescue StandardError => e
      BrowserStack.logger.error("Failed to remove FullconeNat state file: #{e.message}")
    end

    true
  end

  def running?
    File.exist?(state_file)
  end

  private

  def state_file
    "#{BrowserStack::STATE_FILES_DIR}/full_cone_#{@tun_interface_ip}"
  end

  def cleanup_dnat_rules
    BrowserStack.logger.info("Cleaning up DNAT rules for tun interface IP: #{@tun_interface_ip}")
    # Fetch current PREROUTING rules in the nat table
    output, status = Open3.capture2("sudo", "iptables", "-t", "nat", "-S", "PREROUTING")
    rules = output.lines

    deleted_count = 0
    rules.each do |rule|
      rule = rule.strip

      next unless rule.include?("--to-destination")

      # Extract destination IP from --to-destination
      match = rule.match(/--to-destination\s+(\d+\.\d+\.\d+\.\d+):\d+/)

      next unless match && match[1].start_with?(@tun_interface_ip)

      # Convert -A (add) to -D (delete)
      delete_cmd = rule.sub("-A", "-D")

      deleted_count += 1 if system("sudo iptables -t nat #{delete_cmd}")
    end

    BrowserStack.logger.info("Cleanup completed. Removed #{deleted_count} DNAT rules for #{@tun_interface_ip}")
  rescue StandardError => e
    BrowserStack.logger.error("Error during cleanup: #{e.message}")
  end
end
