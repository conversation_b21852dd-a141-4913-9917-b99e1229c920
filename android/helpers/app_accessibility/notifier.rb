# frozen_string_literal: true

require 'faraday'
require 'json'
require 'base64'

require_relative '../../lib/process_reaper'

# Manages asynchronously to notify an external service with results.
class Notifier
  def initialize(context, s3_uploader)
    @context     = context
    @s3_uploader = s3_uploader
  end

  # Forks a process to notify asynchronously.
  #
  # @param url          [String]
  # @param res_json     [Hash]
  # @param automate_obj [Hash]
  def notify_results(url, res_json, automate_obj)
    pid = fork_notify_process(url, res_json, automate_obj)
    ProcessReaper.async_reap(pid)
  end

  private

  # Forks and calls handle_notify_flow.
  #
  # @return [Integer] the PID of the fork
  def fork_notify_process(url, res_json, automate_obj)
    Process.fork do
      start_time = current_time_ms
      handle_notify_flow(url, res_json, automate_obj, start_time)
    end
  end

  # Main flow within the fork: populates device info, handles S3 uploads, POSTs data.
  def handle_notify_flow(url, res_json, automate_obj, start_time)
    populate_device_info_and_log(res_json)

    screenshot_key = upload_screenshot(res_json, automate_obj)
    upload_app_icon(res_json, automate_obj)

    params  = build_request_params(res_json, automate_obj, screenshot_key)
    headers = build_request_headers(automate_obj)
    make_request(url, headers, params)

    log_notify_time(start_time)
  end

  # Populates device info in res_json
  def populate_device_info_and_log(res_json)
    log("res_json deviceInfo => #{res_json[:deviceInfo]}")
    populate_device_info(res_json)
  end

  # Uploads screenshot to S3
  def upload_screenshot(res_json, automate_obj)
    s3_key = generate_screenshot_key(automate_obj)
    @s3_uploader.upload_to_s3(s3_key, Base64.decode64(res_json[:screenshot].to_s), automate_obj)
    res_json.delete(:screenshot)
    s3_key
  end

  # Uploads app icon to S3 (if present)
  def upload_app_icon(res_json, automate_obj)
    return unless res_json.key?(:app_icon)

    s3_key = generate_app_icon_key(automate_obj)
    @s3_uploader.upload_to_s3(s3_key, Base64.decode64(res_json[:app_icon].to_s), automate_obj)
    res_json.delete(:app_icon)
  end

  # Builds request params for final POST
  def build_request_params(res_json, automate_obj, screenshot_key)
    {
      screenshot_key: screenshot_key,
      snapshot_data: res_json,
      test_run_uuid: automate_obj['test_run_uuid'],
      th_build_uuid: automate_obj['th_build_uuid'],
      scan_timestamp: automate_obj['scan_timestamp'],
      session_id: res_json[:sessionId]
    }
  end

  # Builds request headers for final POST
  def build_request_headers(automate_obj)
    {
      "Authorization" => "Bearer #{automate_obj['auth_token']}",
      "Content-Type" => "application/octet-stream"
    }
  end

  # Makes the final POST request
  def make_request(url, headers, params)
    log("Making POST request to #{url} with params: #{params.keys}")
    conn = Faraday.new(url: url, headers: headers, ssl: { verify: false })
    response = conn.post { |req| req.body = params.to_json(max_nesting: 400) }
    log("status of notify_results #{response.status} => #{response.body}")
  rescue StandardError => e
    log("Exception while making request to #{url}: #{e.inspect}")
  end

  # Logs the total time taken for notify
  def log_notify_time(start_time)
    end_time = current_time_ms
    log("Total time taken for notify_results: #{end_time - start_time} ms")
  end

  # Adds resolution and OS details to the deviceInfo sub-hash
  def populate_device_info(res_json)
    device_obj = @context[:device_obj]
    res_json[:deviceInfo][:resolution]  = device_obj.resolution
    res_json[:deviceInfo][:os_version]  = device_obj.os_version
    res_json[:deviceInfo][:device_name] = device_obj.common_name
  rescue StandardError => e
    log("Failed to populate device info: #{e.message}")
  end

  # Generates a unique screenshot key
  def generate_screenshot_key(automate_obj)
    "#{automate_obj['th_build_uuid']}/#{automate_obj['test_run_uuid']}/screenshot-#{SecureRandom.hex(4)}.jpeg"
  end

  # Generates a unique app icon key
  def generate_app_icon_key(automate_obj)
    "#{automate_obj['th_build_uuid']}/#{automate_obj['test_run_uuid']}/app-icon-#{SecureRandom.hex(4)}.jpeg"
  end

  # Returns current time in milliseconds
  def current_time_ms
    (Time.now.to_f * 1000).to_i
  end

  # Logs a message with a standard prefix.
  def log(message)
    BrowserStack.logger.info("[NOTIFIER] #{message}")
  end
end
