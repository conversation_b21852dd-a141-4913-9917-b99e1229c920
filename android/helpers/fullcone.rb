require 'open3'
require 'set'
require 'dotenv/load'
require 'browserstack_logger'
require_relative '../lib/os_utils'

SERVER_LOG = "/var/log/browserstack/server.log".freeze
logger_params = {
  component: "fullcone_script"
}
BrowserStack.init_logger(SERVER_LOG, logger_params)

CHECK_INTERVAL = 0.1  # seconds
IN_INTERFACE = "eno1".freeze

# Get tun_interface_ip from command line argument
TUN_INTERFACE_IP = ARGV[0]
CONNTRACK = ENV['CONNTRACK'] || 'conntrack'

def conntrack_available?
  # Try to run conntrack --version to check if it exists and is working
  stdout, stderr, status = Open3.capture3("sudo", CONNTRACK, "--version")

  if status.success?
    BrowserStack.logger.info("Conntrack is available: #{stdout.strip}")
    true
  else
    BrowserStack.logger.error("Conntrack not available or not working")
    BrowserStack.logger.error("Command: sudo #{CONNTRACK} --version")
    BrowserStack.logger.error("Exit code: #{status.exitstatus}")
    BrowserStack.logger.error("Error output: #{stderr}") unless stderr.empty?
    false
  end
rescue StandardError => e
  BrowserStack.logger.error("Error checking conntrack availability: #{e}")
  false
end

def conntrack_udp_entries
  output, stderr, status = Open3.capture3("sudo", CONNTRACK, "-L", "-p", "udp")
  entries = Set.new

  unless status.success?
    BrowserStack.logger.error("Error with conntrack command: #{stderr}")
    return entries
  end

  output.each_line do |line|
    # Match conntrack UDP entry with source and destination info
    pattern = 'src=(\d+\.\d+\.\d+\.\d+)\s+dst=(\d+\.\d+\.\d+\.\d+)\s+sport=(\d+)\s+dport=(\d+)' \
              '.+?src=(\d+\.\d+\.\d+\.\d+)\s+dst=(\d+\.\d+\.\d+\.\d+)\s+sport=(\d+)\s+dport=(\d+)'
    match = line.match(Regexp.new(pattern))

    next unless match

    internal_src_ip = match[1]
    internal_src_port = match[3]
    public_dst_ip = match[6]
    public_dst_port = match[8]

    entries.add([public_dst_port, internal_src_ip, internal_src_port]) if internal_src_ip.start_with?(TUN_INTERFACE_IP)
  end

  entries
rescue StandardError => e
  BrowserStack.logger.error("Error reading conntrack: #{e}")
  Set.new
end

def current_prerouting_rules
  output, stderr, status = Open3.capture3("sudo", "iptables", "-t", "nat", "-S", "PREROUTING")
  rules = Set.new

  unless status.success?
    BrowserStack.logger.error("Error with iptables command: #{stderr}")
    return rules
  end

  output.each_line do |line|
    match = line.match(/--dport (\d+) -j DNAT --to-destination (\d+\.\d+\.\d+\.\d+):(\d+)/)

    next unless match

    rules.add([match[1], match[2], match[3]])
  end

  rules
rescue StandardError => e
  BrowserStack.logger.error("Error getting iptables rules: #{e}")
  Set.new
end

def add_dnat_rule(port, internal_ip, internal_port)
  cmd = [
    "sudo", "iptables", "-t", "nat", "-A", "PREROUTING",
    "-i", IN_INTERFACE, "-p", "udp", "--dport", port,
    "-j", "DNAT", "--to-destination", "#{internal_ip}:#{internal_port}"
  ]

  OSUtils.execute(cmd.join(" "))
end

def delete_dnat_rule(port, internal_ip, internal_port)
  cmd = [
    "sudo", "iptables", "-t", "nat", "-D", "PREROUTING",
    "-i", IN_INTERFACE, "-p", "udp", "--dport", port,
    "-j", "DNAT", "--to-destination", "#{internal_ip}:#{internal_port}"
  ]

  OSUtils.execute(cmd.join(" "))
end

def main_loop
  BrowserStack.logger.info("Starting fullcone NAT for tun interface IP: #{TUN_INTERFACE_IP}")

  loop do
    seen_now = conntrack_udp_entries
    existing_rules = current_prerouting_rules

    # Add new rules
    seen_now.each do |entry|
      next if existing_rules.include?(entry)

      add_dnat_rule(*entry)
    end

    # Clean up expired rules
    existing_rules.each do |existing_rule|
      next if seen_now.include?(existing_rule)

      delete_dnat_rule(*existing_rule)
    end

    sleep(CHECK_INTERVAL)
  end
end

if __FILE__ == $PROGRAM_NAME
  if TUN_INTERFACE_IP
    unless conntrack_available?
      BrowserStack.logger.error("Cannot start fullcone NAT: conntrack is not available")
      exit 1
    end

    main_loop
  else
    BrowserStack.logger.error("Error: TUN_INTERFACE_IP not provided as argument")
    BrowserStack.logger.error("Usage: ruby fullcone.rb <tun_interface_ip>")
    exit 1
  end
end
