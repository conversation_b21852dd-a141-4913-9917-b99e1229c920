#!/bin/bash

####                                                                                                ####
#   WARNING: THIS FILE CANNOT EXECUTE ON IT'S OWN, THIS NEEDS TO BE SOURCED INSIDE driver_actions.sh   #
####                                                                                                ####


dedicated_cleanup() {
  set -x
  TUN_IP=$1
  RETRY_CLEANUP=$2
  FULL_CLEANUP=$3

  cleanup_timer_start=$(date +%s)

  log_cleanup_flow "dedicated_cleanup started"
  log_cleanup_flow "    \$FULL_CLEANUP:  $FULL_CLEANUP"
  log_cleanup_flow "    \$FACTORY_RESET: $FACTORY_RESET"
  log_cleanup_flow "    \$RETRY_CLEANUP: $RETRY_CLEANUP"
  is_dedicated_cleanup
  cleanup_type="dedicated_cleanup"
  # delete app for this device
  delete_app

  session_id=`mobile_session_get "session_id" | tr -d '"'`

  # instrument functions in this block
  record_time check_device_on_adb
  record_time check_frozen
  record_time bsrun_force_unlock
  record_time clean_in_session
  record_time record_time alert_device_rooted
  record_time start_check_adb_connection
  record_time reset_device_preferences
  record_time enable_animations
  record_time disconnect_call
  record_time remove_sim_popup
  record_time stop_performance_statistics_service
  record_time enable_play_store
  record_time ensure_essential_binaries
  record_time kill_device_binaries
  record_time reduce_volume
  record_time push_user_installed_applist_to_cls
  record_time ensure_locale
  record_time disable_samsung_dex
  record_time check_user_screen_lock_set
  record_time reset_proxy_configuration
  record_time set_device_datetime
  record_time grant_permission_and_push_vcf_for_preload_contacts
  record_time launch_activity_for_preload_contacts
  record_time skip_overlay
  record_time stop_mock_location_service
  record_time disable_bluetooth
  record_time clear_devtools_forwards
  record_time disable_chrome_welcome_screen
  record_time check_for_preloaded_contacts
  record_time check_fingerprint_sensor
  record_time disable_hardware_lpm
  record_time ensure_home_screen_focused
  record_time update_settings
  record_time tell_rails_that_the_cleanup_is_done
  record_time ensure_run_as_root_working
  record_time clean_network_simulation
  record_time stop_mitm
  record_time reset_custom_props
  record_time handle_reboot_in_cleanup
  record_time check_outside_browser
  record_time check_for_app_crash
  record_time check_scrcpy_detected
  record_time contacts_cleanup
  record_time check_deprecated_app_detected
  record_time check_vpn_app_and_forwarder
  record_time ensure_hidden_api_policy_enabled
  record_time log_trichromelibrary
  record_time reset_status_bar
  record_time install_apks
  record_time kill_processes
  record_time remove_session_file
  record_time log_check_statusbar
  record_time check_battery
  record_time reinstall_oppo_webview
  record_time check_apks_installed
  record_time change_language
  record_time clear_gboard
  record_time check_device_rooted
  record_time check_internet_stability
  record_time verify_and_install_bs_app
  record_time disable_huawei_browser
  record_time set_default_huawei_keyboard
  record_time set_hidden_policy
  record_time install_screenrecord_binary
  record_time remove_session_related_file
  record_time check_internet_and_fix
  record_time dismiss_location_tag_popup
  record_time dismiss_browser_upgrade_popup
  record_time screenrecord_long_press_and_rescue_party
  # end block

  kill_processes

  ############################### ------------------------------------------------------- [BEGIN]  Pre Cleanup Steps Moved to Ruby ------------------------------- ########################
  # Pre Cleanup Steps -> check_device_on_adb, check_frozen
  # check_device_on_adb -> Any ADB commands related to the device should be after this check

  run_ruby_code $PRE_CLEANUP "check_device_stability" $DEVICEID $last_session_id
  local check_device_stability_exit_code=$?

  if [[ ! $check_device_stability_exit_code -eq 0 ]]; then
    fail_cleanup_with_reason "Failed device stability check, reason: $exit_msg"
  fi
  ############################### --------------------------------------------------------------- [END]  Pre Cleanup Steps  -------------------------------------- ########################

  if device_is_a bsrun_device; then
    ensure_run_as_root_working
  fi

  #check that uiautomation and browserstack apps are present before running any commands which require either
  install_via_ruby $BROWSERSTACK_APP_MANAGER
  install_via_ruby $UI_AUTOMATION_APPS_MANAGER

  # Sometimes recovery devices are wrongly booted, and some root commands gets failed.
  run_ruby_code $ANDROID_DEVICE $DEVICEID "cleanup" "ensure_correct_boot_mode"

  get_device_model
  get_device_manufacturer
  get_os_version
  bsrun_force_unlock
  clean_dont_log_netcheck
  clean_in_session
  fetch_last_session_info

  remove_session_file

  ############################### ------------------------------------------------------- [BEGIN]  Pre Cleanup Steps to upload logs ------------------------------- ########################
  # Pre Cleanup Steps -> handle_logs, instrument_crypto_mining
  log_cleanup_flow "Executing PreCleanup Steps"
  run_ruby_code $PRE_CLEANUP "run" $DEVICEID $last_session_id $last_session_type
  ############################### --------------------------------------------------------------- [END]  Pre Cleanup Steps  -------------------------------------- ########################

  check_device_rooted
  # Disable apps through db - MOBFR-816
  run_ruby_code "$DISABLE_APPS_SCRIPT" "$DEVICEID"

  echo "Starting check adb connection script (first attempt, at the beginning of cleanup)"
  start_check_adb_connection

  check_vpn_app_and_forwarder

  reset_device_preferences

  status_check+="$TUN_IP   pint: $($timeout 2 $ADB -s $DEVICEID shell "cat /sdcard/status 2>&1")"
  enable_animations

  status_check+="1: $($timeout 2 $ADB -s $DEVICEID shell "cat /sdcard/status 2>&1")"
  disconnect_call

  status_check+="2: $($timeout 2 $ADB -s $DEVICEID shell "cat /sdcard/status 2>&1")"
  remove_sim_popup

  status_check+="3: $($timeout 2 $ADB -s $DEVICEID shell "cat /sdcard/status 2>&1")"
  stop_performance_statistics_service

  status_check+="4: $($timeout 2 $ADB -s $DEVICEID shell "cat /sdcard/status 2>&1")"
  enable_play_store

  status_check+="de: $($timeout 2 $ADB -s $DEVICEID shell "cat /sdcard/status 2>&1")"
  if [ -f $NETWORK_SIMULATION_FILE ]; then
    clean_network_simulation
  fi

  run_ruby_code $BATTERY_HELPER 'stop_mocking_properties' $DEVICEID $OS_VERSION

  log_check_statusbar

  run_ruby_code $MAIN_CLEANUP 'handle_security_alerts' $DEVICEID $session_id $last_session_type

  stop_mitm

  ensure_essential_binaries
  kill_device_binaries
  reduce_volume

  run_ruby_code "$BS_DIR/mobile/android/lib/network_usage_tracker.rb" "$last_session_id" "$DEVICEID" "$last_session_type" "track" &

  if [[ "$last_session_type" == "app_automate" ]] && [[ ( ! -f $APP_INTERNET_VIA_USB_FILE ) || ( ! -s $APP_INTERNET_VIA_USB_FILE ) ]] && [[ ( ! -f $NETWORK_SIMULATION_FILE ) || ( -f $LIMIT_NETWORK_FILE ) ]]; then # pushing stability reason when app traffic is not flowing via usb (non usb flow/skip usb flow) and whenever network_simulation feature is used
    run_ruby_code "$BS_DIR/mobile/android/lib/router_healthcheck.rb" "$last_session_id" "$DEVICEID" "$OS_VERSION" "stop"
  fi


  if [[ "$last_session_type" == "speedlab" ]]; then
    echo "Deleting files for speedlab session"
    $ADB -s "$DEVICEID" shell rm  -f  /storage/self/primary/browsertime.mp4
  fi

  reset_custom_props

  check_battery

  echo "Cleaning up"
  fetch_props $DEVICEID


  if [ "$OS_VERSION" == "unknown" ] || [ -z "$OS_VERSION" ]; then
    log "Don't know OS Version. Trying to fetch from adb"
    get_os_version
  fi
  set_browser_only_flag "$device_model" "$OS_VERSION"

  log_cleanup_flow "    \$BROWSER_ONLY: $BROWSER_ONLY"

  cleanup_model=$(echo $device_model | tr ' ' _ | tr - _ | tr -d '"')
  cleanup_tag_prefix=$(cat $BS_DIR/mobile/android/device_mapping.json | grep -w "$cleanup_model" | awk '{print $NF}' | tr -d '",')
  cleanup_os_version=$(echo $OS_VERSION | tr . "_" | tr -d '"')

  push_user_installed_applist_to_cls

  if device_is_a reboot_after_session || [[ -f $NEEDS_REBOOT_FILE ]] || [[ -f $NEEDS_REBOOT_FOR_INSTALL_FAILURE_FILE ]]; then
    handle_reboot_in_cleanup
  fi

  check_outside_browser

  check_internet_stability

  check_for_app_crash

  check_scrcpy_detected
  check_deprecated_app_detected

  # Huawei P30 devices somehow get in a state where the BS app isn't installed.
  # Re-installing it for cleanup to work and pushing the event.
  # Disabling Huawei Browser
  # Set SwiftKey as default keyboard
  if [[ "$device_model" = "ELE-L04" ]] || [[ "$device_model" = "ELE-L09" ]] || [[ "$device_model" = "ELE-L29" ]]; then
    verify_and_install_bs_app
    disable_huawei_browser
    set_default_huawei_keyboard
  fi

  # Ensure that the UI automation APKs are installed
  # Before attempting Factory Reset, or
  # Before attempting to remove Google Account
  install_via_ruby $UI_AUTOMATION_APPS_MANAGER

  ensure_locale

  check_vpn_app_and_forwarder

  # only pushing internet stability reason for AA and AUT sessions
  # not pushing if network_simulation feature is used
  if [[ ( ! -f $NETWORK_SIMULATION_FILE ) || ( -f $LIMIT_NETWORK_FILE ) ]]; then
    run_ruby_code $INTERNET_HEALTH_CHECKER "$DEVICEID" "$last_session_type" "process_failures" "$last_session_id"
  fi

  if vers_gte "$OS_VERSION" "8"; then
    $BUNDLE exec ruby $BROWSERSTACK_WATCHER_HELPER "$DEVICEID" "$last_session_id" "$last_session_type" "restart"
  fi

  check_user_screen_lock_set
  if [[ $(should_clean_for_dedicated "clean_browser") -eq 1 ]]; then browser_reset; fi
  stop_input_injector

  # This is used to truncate logs and delete state files
  run_ruby_code $INTERNET_HEALTH_CHECKER "$DEVICEID" "$last_session_type" "truncate_logs_and_delete_state_file" "$last_session_id" &

  if [[ "$device_model" == "CPH2035" ]]; then
    # Uninstall phone manager app. This app can cause a popup incorrectly
    # identifying Browserstack app as a Trojan virus. See: MOBPL-1012
    "$ADB" -s "$DEVICEID" shell "pm uninstall --user 0 com.coloros.phonemanager"

    # Re installing webview package on Oppo Reno 3 Pro to ensure it is working
    # and disabling Opera and Oppo browsers. Chrome & Firefox should be available
    reinstall_oppo_webview
  fi

  if [[ "$manufacturer" == "HUAWEI" ]]; then
    run_ruby_code $SETTINGS_HELPER "disable_touch_disable_mode" "$DEVICEID"
  fi

  if [ "$device_model" == "SM-N970F" ] || [ "$device_model" == "SM-N976B" ]; then
      # This freezes Note 10 phones
      disable_samsung_dex
  fi

  if [[ "$device_model" =~ "Pixel" ]]; then
    # Killing the setupwizard is flaky for the Pixels, make sure its disabled after factory reset
    run_as_root "pm disable com.google.android.setupwizard"
  fi


  # https://browserstack.atlassian.net/browse/MOB-9245
  if [[ "$device_model" == "Pixel 6" ]]; then
    screenrecord_long_press_and_rescue_party
  fi

  # This needs to be done before the app that is going to use the
  # hidden API is launched or it has no effect, hence doing ASAP.
  # One of the main users is the bstack app, to change language.
  ensure_hidden_api_policy_enabled

  # Add wifi network without mac randomisation for v12 devices
  # More info here: https://browserstack.atlassian.net/browse/MOBPE-229
  if [[ "$OS_VERSION" == "12" ]];
  then
    log_cleanup_flow "Adding $WIFI_SSID network with MAC Randomisation disabled"
    $ADB -s $DEVICEID shell "cmd wifi add-network $WIFI_SSID wpa2 $WIFI_PASSWORD -r none"
  fi

  log_cleanup_flow "Reseting proxy + other stuff"
  reset_proxy_configuration
  zombie_push_cleanup $cleanup_type $cleanup_reason
  cleanup_stats_to_hoothoot "$cleanup_type" "$cleanup_tag_prefix" "$cleanup_os_version"

  if [ -f $BRT_STARTED_FOR_BSTACK_APPS_FILE ]; then
    log_cleanup_flow "Restarting BRT for all apps"
    run_ruby_code $BSTACK_REVERSE_TETHER_CONTROLLER "$DEVICEID" "restart"
    $ADB -s $DEVICEID shell cmd appops set com.android.browserstack CHANGE_WIFI_STATE allow
    rm $BRT_STARTED_FOR_BSTACK_APPS_FILE
  fi

  # Reinstall all app things.
  SESSION_GENRE='Cleanup' # Tmp assignment to debug https://browserstack.atlassian.net/browse/MOB-2787
  check_screen  # @TODO Check in detail if this is necessarily required
  if [[ "$device_model" == "SM-G965F" ]]; then # Sleep 20 for S9 Plus devices going off adb after check_screen[https://browserstack.atlassian.net/browse/MOB-9680]
    sleep 20
  else
    sleep 5
  fi
  set_device_datetime

  log_cleanup_flow "Install apps check"

  if [[ -f $FORCE_PUBLIC_CLEANUP_FILE ]]; then
    log_cleanup_flow "    Force public cleanup file found, reinstalling AndroidRTC"
    run_ruby_code $INSTALL_APKS "install_android_rtc_app" $DEVICEID $COMPONENT $session_id $last_session_type
    rm $FORCE_PUBLIC_CLEANUP_FILE
  fi

  check_apks_installed

  run_ruby_code "$MAIN_CLEANUP" "fix_playstore_permissions" $DEVICEID $session_id $last_session_type

  ensure_locale
  install_via_ruby $CAMERA_CHECK_MANAGER "cleanup-apps-check"
  run_popup_handler

  check_appium_apks_default_version_installed
  check_appium_apks

  set_package_verifier_user_consent "-1"
  install_via_ruby $CHROME_RELEASE_INSTALLER "cleanup-apps-check"
  install_via_ruby $UI_AUTOMATION_APPS_MANAGER

  # Check and install check_status_bar_scripts
  install_via_ruby $RESTRICTION_SCRIPTS_MANAGER

  uninstall_pre_installed_apks

  if [[ $(device_in_bstack_vpn_list) == "false" ]] && device_is_a uses_gnirehtet_for_app_automate; then
    log_cleanup_flow "Ensuring gnirehtet popup is handled"
    run_ruby_code $USB_VPN_RUBY $DEVICEID ensure_gnirehtet_has_vpn_permissions
    run_ruby_code $USB_VPN_RUBY $DEVICEID start_gnirehtet_relay &
  fi

  # Get rid of potential popups before running UI Automation code below
  run_ruby_code $POPUP_HELPER "dismiss_deprecated_app_popup" "$DEVICEID" "$OS_VERSION" "$session_id"

  if [[ "$cleanup_type" == "full_cleanup" ]]; then
    run_ruby_code $UI_AUTOMATION_HELPER_SCRIPT "handle_location_popups" "$DEVICEID" "$OS_VERSION"
  fi

  needs_extra_hidden_api_policy_check=$(device_attribute "$device_model" "$OS_VERSION" "needs_extra_hidden_api_policy_check")

  if [[ needs_extra_hidden_api_policy_check == "true" ]]; then
    set_hidden_policy
  fi

  run_ruby_code $BATTERY_UPDATE_SCRIPT $DEVICEID &

  ############################### ------------------------------------------------------- [BEGIN]  checking if internet is up ------------------------------- ########################
  if [[ $(device_in_bstack_vpn_list) == "true" ]]; then
    brt_check_wifi_and_fix
    brt_check_usb_internet_and_fix
  else
    check_internet_and_fix
  fi

  ############################### ------------------------------------------------------- [END]  checking if internet is up ------------------------------- ########################

  if [[ "$cleanup_type" == "full_cleanup" ]] && [[ "$manufacturer" == "samsung" ]] && vers_gte "$OS_VERSION" "7"; then
    dismiss_location_tag_popup
  fi

  if [[ "$manufacturer" == "samsung" ]] && vers_gte "$OS_VERSION" "12"; then
    dismiss_browser_upgrade_popup
  fi

  log_cleanup_flow "final cleanup steps"

  if [[ "$device_model" == "SM-G960F" ]]; then
    change_language
  fi

  clear_gboard

  if [ "$BROWSER_ONLY_DEVICE" == "false" ]; then
    if [[ "$count" == 0 ]]; then
      if [[ $(device_in_bstack_vpn_list) == "false" ]]; then
        check_vpn
      else
        echo "Device in Bstack VPN list, ensure_running already completed"
      fi
    fi
    if [ "$cleanup_type" == "full_cleanup" ]; then
      safety_service

      # Disable recent onboarding steps
      if [[ "$device_model" == "Pixel 3" ]]; then
        disable_recent_onboarding_tips
      fi
    else
      grant_permissions
    fi
    disable_privacy_notes
  fi
  skip_overlay
  stop_mock_location_service
  run_ruby_code "$CAMERA_CHECK_SCRIPT" $DEVICEID
  disable_bluetooth
  clear_devtools_forwards
  disable_chrome_welcome_screen
  check_for_preloaded_contacts
  check_fingerprint_sensor
  run_ruby_code "$LIVE_MEDIA_INJECTOR_HELPER" "inject_sample_media" "$DEVICEID" &

  # Nexus 6P is Live only, so play store is disabled anyway.
  # It will start downloading system updates if gms is unblocked.
  if [[ $device_model == *"Nexus 6P"* ]]; then
    run_as_root "pm disable com.google.android.gms"
  fi

  run_ruby_code $ANDROID_DEVICE $DEVICEID "bash_cleanup" "mitm_cert_installed?"
  if [[ "$cleanup_type" == "full_cleanup" || $exit_msg == "false" ]]; then
    check_and_install_mitm_ca_cert "full_cleanup"
  fi

  run_ruby_code $ACCOUNT_HELPER 'instrument_accounts' $DEVICEID

  if [[ -z "$statusoutput" ]] && [[ ! -z "$is_wifi_up" ]]; then
    $COMMON/push_to_zombie.rb "android" "cleanup-installation-retry" "$count" "$DEVICEID" "$device_model" &
    push_to_influxdb_v1 "cleanup-installation-retry" "cleanup" "cleanup" "$DEVICEID" "false" &
    # trigger prestart: live_actions.sh prestart device_id interaction_args screen_width
    #/usr/local/.browserstack/mobile/android/live/scripts/live_actions.sh pre_start $DEVICEID "$interaction_args" "" $screen_width &

    echo "[$DEVICEID] Cleanup done"
  else
    $COMMON/push_to_zombie.rb "android" "cleanup-retry-exceeded" "$count" "$DEVICEID" "$device_model" &
    push_to_influxdb_v1 "cleanup-retry-exceeded" "cleanup" "cleanup" "$DEVICEID" "true" &
    echo "[$DEVICEID] Cleanup done but /sdcard/status not empty: $statusoutput, or wifi is down: $is_wifi_up"
  fi

  # trigger prestart: live_actions.sh prestart device_id interaction_args screen_width
  #/usr/local/.browserstack/mobile/android/live/scripts/live_actions.sh pre_start $DEVICEID "$interaction_args" "" $screen_width &

  # The phone is ready for the user, but for some models it still has a
  # su/magisk binary and other stuff. Ensure that we give an unrooted phone.
  if device_is_a needs_unroot; then
    unroot_device
  fi

  # Disable emergency alerts for Android 11+
  if vers_gte "$OS_VERSION" "11"; then
    run_ruby_code $EMERGENCY_ALERTS_SCRIPT "disable" "$DEVICEID"
  fi

  echo "Cleaning up orientation"
  $ADB -s $DEVICEID shell am startservice --user 0 -n com.android.browserstack/.services.OrientationService --es "orientation" "portrait"

  $ADB -s $DEVICEID shell dumpsys wifi | grep mNetworkInfo

  if [[ "$manufacturer" == "samsung" ]]; then
    disable_mtp

    if vers_gte "$OS_VERSION" "9"; then
      disable_samsung_friends
    fi

    # Bixby isn't present on older Samsung devices.
    # Check https://browserstack.atlassian.net/browse/MOB-6796
    if [[ "$cleanup_type" == "full_cleanup" ]] && vers_gte "$OS_VERSION" "7"; then
      echo "Disabling bixby packages"
      disable_bixby_packages
      echo "Disabled bixby packages"
    fi

    if vers_lte "$OS_VERSION" "6"; then # for greater than 6 we already ensure default browser is set as chrome
      run_ruby_code "$SAMSUNG_BROWSER_MANAGER" "$DEVICEID" "remove_as_default"
    fi
  fi

  #Ensure that esim popup is not present, currently just doing this for S21s, see MOB-9702
  if [[ "$device_model" == "SM-G991B" ]]; then
    run_ruby_code $POPUP_HELPER "dismiss_esim_popup" "$DEVICEID" "$OS_VERSION" "$session_id"
  fi

  # Install screenrecord binary for OPPO or realme device
  if device_is_a uses_alternative_screenrecord; then
    install_screenrecord_binary
  fi

  echo "Starting check adb connection script (very last thing, to ensure it's running)"
  start_check_adb_connection
  disable_hardware_lpm

  # temporary check to see devices automatically rotating to landscape
  rotation_angle="$($timeout 10 $ADB -s $DEVICEID shell "settings get system accelerometer_rotation" | tr -d '[[:space:]]')"
  if [[ "$rotation_angle" == "1" ]]; then
    $ZOMBIE "android" "rotation-landscape-after-cleanup" "" "" "" "$DEVICEID"
  fi

  if device_is_a foldable_device; then
    run_ruby_code $MAIN_CLEANUP "reset_foldable_screen" "$DEVICEID" $session_id $last_session_type
  fi

  ensure_home_screen_focused
  update_settings

  log "removing session, cleanupdone and reboot file"
  session_type=`cat /tmp/sessionis_$DEVICEID | tr -d "\r\n"`
  remove_mobile_session_dir
  remove_session_related_file

  $ADB -s $DEVICEID shell "echo '' > $TEMP_CLEANUP_FILE; rm -f $SESSION_DIR/*;"
  if [[ -f /tmp/custom_deploy_$DEVICEID.sh ]]; then
    bash /tmp/custom_deploy_$DEVICEID.sh;
    rm /tmp/custom_deploy_$DEVICEID.sh;
  fi
  if [[ "$session_type" == "espresso" ]]; then
    rm -rf /tmp/espresso*_$DEVICEID
  fi

  tell_rails_that_the_cleanup_is_done

  log_cleanup_flow "Sending device process tracking to zombie"
  run_ruby_code $PROCESS_HELPER "$DEVICEID" "$session_id" "log_port_scanning_detection"

  cleanup_timer_end=$(date +%s)

  if [ ! -z $reboot_start_timer ]; then
    reboot_elapsed_seconds=`expr $reboot_end_timer - $reboot_start_timer`
  else
    reboot_elapsed_seconds=0
  fi

  elapsed_seconds=`expr $cleanup_timer_end - $cleanup_timer_start - $reboot_elapsed_seconds`

  $COMMON/push_to_zombie.rb "android" "android_$cleanup_type" "$last_session_type" "" "$elapsed_seconds" "$DEVICEID" "$last_session_id" "$reboot_elapsed_seconds"
  execution_time_to_hoothoot "mobile_cleanup_time" "$elapsed_seconds" "$device_model" "$cleanup_type"

  log_cleanup_flow "Sending cleanup stats to zombie"
  run_ruby_code $CLEANUP_STATS_HELPER "$DEVICEID" "$cleanup_type" "$elapsed_seconds" "$session_id"

  log_cleanup_flow "-- END CLEANUP --"
}
