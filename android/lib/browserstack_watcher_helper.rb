require_relative '../constants'
require_relative '../../common/push_to_zombie'
require_relative '../exit_file'
require_relative '../../common/helpers'

require 'android_toolkit'
require 'logger'
require 'json'

# This is a controller for performing all the actions related to watcher app
# It is used currently only for App Automate,
# AppLive sessions and for S21 and P30 devices between cleanup and session start
class WatcherHelper
  PACKAGE_NAME = 'com.browserstack.watcher'.freeze
  WATCHER_ACTIVITY = 'com.browserstack.watcher/com.browserstack.watcher.services.Watcher'.freeze
  LOGDUMP_ACTIVITY = 'com.browserstack.watcher/com.browserstack.watcher.services.LogDump'.freeze
  SESSIONINFO_ACTIVITY = 'com.browserstack.watcher/com.browserstack.watcher.services.SessionInfo'.freeze
  NETWORK_MONITOR_ACTIVITY = 'com.browserstack.watcher/com.browserstack.watcher.services.NetworkMonitor'.freeze
  LOGFILE_PATH = '/sdcard/Android/data/com.browserstack.watcher/files/watcher.txt'.freeze
  KIND = 'popup-watcher-stats'.freeze

  def initialize(device_id, session_id, genre, logger)
    raise "Device cannot be empty" if device_id.to_s.empty?

    @device_id = device_id
    @session_id = session_id
    @genre = genre
    @logger = logger
    @adb = AndroidToolkit::ADB.new(udid: @device_id, path: BrowserStack::ADB)
    AndroidToolkit::Log.logger = @logger
  end

  def start_accessibility_service
    @adb.shell("settings put secure enabled_accessibility_services #{WATCHER_ACTIVITY}")
  end

  def stop
    @adb.shell("settings put secure enabled_accessibility_services null")
    @adb.shell("am force-stop #{PACKAGE_NAME}")
  end

  def check_if_running
    log(:info, "checking if watcher is running")
    output = @adb.shell("settings get secure enabled_accessibility_services | grep '#{WATCHER_ACTIVITY}'")

    if output.strip.empty?
      nil
    else
      log(:info, "watcher is running")
      output.strip
    end
  rescue AndroidToolkit::ADB::ExecutionError => e
    log(:error, "function call failed due to adb error: #{e.message}")
    push(e.message[0..100], "adb error")
    nil
  end

  def cont_scanning_state_file_exists?
    log(:info, "checking if cont scanning state file exists")
    output = @adb.shell("run-as #{PACKAGE_NAME} ls #{BrowserStack::WATCHER_WORKING_DIR}").split("\n")
    if output.include?(BrowserStack::CONTINUOUS_SCANNING_STATE_FILE)
      log(:info, "continuous scanning state file exists")
      true
    else
      log(:info, "continuous scanning state file doesn't exist")
      false
    end
  rescue StandardError => e
    log(:error, "Error while checking continuous scanning state file: #{e.message}")
    false
  end

  def set_genre
    @adb.shell("am startservice --user 0 -n #{SESSIONINFO_ACTIVITY} --es genre #{@genre} &")
  end

  def add_flags_for_testbed_collection(user_bundle_id)
    @adb.shell("am startservice --user 0 -n #{SESSIONINFO_ACTIVITY} --es genre #{@genre} "\
               " --es testbedBundleId #{user_bundle_id} &")
  end

  def start_network_monitor
    @adb.shell("am startservice --user 0 -n #{NETWORK_MONITOR_ACTIVITY} &")
  end

  def delete_continuous_scanning_state_file
    @adb.shell(
      "run-as #{BROWSERSTACK_WATCHER_PACKAGE_NAME} " \
      "rm -f #{WATCHER_WORKING_DIR}/#{CONTINUOUS_SCANNING_STATE_FILE}"
    )
    log(:info, "Deleted continuous scanning state file")
  rescue StandardError => e
    log(:error, "Failed to delete continuous scanning state file: #{e.message}")
  end

  def watcher_running?
    output = @adb.shell("dumpsys activity processes | grep #{BROWSERSTACK_WATCHER_PACKAGE_NAME}")
    output.include?(BROWSERSTACK_WATCHER_PACKAGE_NAME)
  rescue StandardError => e
    log(:error, "watcher_running? check failed: #{e.message}")
    false
  end

  def pending_tasks
    out = @adb.shell("am broadcast -a com.browserstack.watcher.GET_ACTIVE_TASK_COUNT")
    out[/result=(\d+)/, 1].to_i
  rescue StandardError => e
    log(:error, "pending_tasks failed: #{e.message}")
    0
  end

  def dump_logs
    @adb.shell("am startservice --user 0 -n #{LOGDUMP_ACTIVITY} &")
  end

  def log(level, msg)
    @logger.send(level.to_sym, "#{self.class} #{msg}")
  end

  def process_logs
    stop
    error = nil
    logs = @adb.shell("cat #{LOGFILE_PATH}")
    error = "empty_nil_log_dump" if logs.nil? || logs.empty?

    log(:info, "Android watcher logs: #{logs}")
    push(logs, error)
    @adb.shell("truncate -s 0 #{LOGFILE_PATH}")
  rescue AndroidToolkit::ADB::ExecutionError => e
    log(:error, "process_logs failed due to adb errror: #{e.message}}")
    push(e.message[0..100], "process_logs_exception")
  end

  def start_from_cleanup
    log(:info, "Starting watcher app")
    start_accessibility_service
    sleep 0.1
    set_genre
    start_network_monitor
  rescue StandardError => e
    log(:error, "[BROWSERSTACK_WATCHER] Failed to start accessibility service: #{e.message} "\
                 "backtrace: #{e.backtrace.join('\n')}.")
  end

  def restart
    if cont_scanning_state_file_exists?
      log(:info, "Continuous scanning state file exists, skipping restart")
      return
    end

    dump_logs
    sleep 1
    process_logs
    sleep 1
    start_from_cleanup

    log(:info, "Restarted watcher")
  end

  def self.run_from_bash
    device_id = ARGV[0].to_s.strip
    session_id = ARGV[1].to_s.strip
    genre = ARGV[2].to_s.strip
    command = ARGV[3].to_s.strip.downcase.to_sym

    logger = Logger.new($stdout)

    watcher_helper = WatcherHelper.new(device_id, session_id, genre, logger)
    watcher_helper.send(command)
  rescue StandardError => e
    ExitFile.write(e.message)
    raise e
  end

  def add_google_restriction_file_to_watcher
    unless google_signup_blocking_enabled?
      log(:info, "[BROWSERSTACK_WATCHER add_google_restriction_file_to_watcher] google signup "\
                  "blocking not enabled for product #{@genre}")
      return
    end

    ls_dir = @adb.shell("run-as #{PACKAGE_NAME} ls /data/user/0/#{PACKAGE_NAME}").split("\n")
    @adb.shell("run-as #{PACKAGE_NAME} mkdir /data/user/0/#{PACKAGE_NAME}/files") unless ls_dir.include?("files")
    @adb.shell("run-as #{PACKAGE_NAME} touch /data/user/0/#{PACKAGE_NAME}/files/googlesignup_block.txt")
  rescue AndroidToolkit::ADB::ExecutionError => e
    log(:error, "[BROWSERSTACK_WATCHER add_google_restriction_file_to_watcher] Failed to add google restriction "\
                 "file to watcher: #{e.message} backtrace: #{e.backtrace.join('\n')}.")
  end

  def google_signup_blocking_enabled?
    ["app_live_testing", "live_testing"].include?(@genre)
  end

  private

  def push(data, error)
    return if @session_id.nil?

    log(:info, "pushing #{KIND} failures to zombie")
    zombie_key_value(
      platform: 'android',
      kind: KIND,
      browser: @genre,
      data: data,
      error: error,
      device: @device_id,
      session: @session_id
    )
  end
end

WatcherHelper.run_from_bash if $PROGRAM_NAME == __FILE__
