require_relative "constants"
require_relative 'models/android_device'
require_relative 'server'
require_relative 'helpers/crypto_mining_detection_helper'
require_relative 'helpers/session_save_polling'
require_relative 'helpers/browser_activity_monitoring'
require_relative 'helpers/fullcone_nat_manager'
require_relative './version_managers/ui_automation_apps_manager'
require_relative './version_managers/browserstack_app_manager'
require_relative "./lib/os_utils"
require_relative './models/device_state'
require '/usr/local/.browserstack/mobile-common/utils/time_recorder'
require 'android_toolkit'
require 'logger'
require 'fileutils'
require 'static_conf'
require 'browserstack_utils'

BS_HOME = "/usr/local/.browserstack".freeze
DIR_HOME = "#{BS_HOME}/mobile".freeze
SELENIUM_SCRIPT = "#{DIR_HOME}/android/helpers/selenium_helper.sh".freeze
APP_LIVE_SCRIPT = "#{DIR_HOME}/android/live/scripts/app_live_actions.sh".freeze

# This PreCleanup class is created to move the necessary steps which needs to be run before we
# actually start with the cleaning of device.
# Our goal will be moving all the pre cleanup step in this PreCleanup class

class PreCleanup # rubocop:disable Metrics/ClassLength
  include TimeRecorder

  methods_to_wrap = %i[
    handle_device_logs
    handle_network_logs
    handle_appium_logs
    handle_crash_logs
    handle_screenshot_logs
    handle_app_live_logs
    stop_video_recording
    handle_logs
    log_crypto_mining_detection
    kill_app_download_process
    kill_app_install_process
    remove_async_app_install_files
    check_and_install_ui_automation_apps
    check_and_install_browserstack_app
  ]

  # around_method takes a list of method names, and a block to be executed which wraps around the original method,
  # see helpers/method_interceptor.rb
  around_method(*methods_to_wrap) do |original_method, method_name|
    record_time(method_name, device_id, "android") { original_method.call }
  end

  attr_reader :device_id, :session_id

  def initialize(device_id, session_id, logger, logger_params = {}, genre = nil)
    @device_id = device_id
    @session_id = session_id
    @logger = logger
    @logger_params = logger_params
    @genre = genre
    @device_state = DeviceState.new(device_id)

    AndroidToolkit::Log.logger = @logger
  end

  def execute_cmd(progname, function, script, log_file, params='')
    script_logger = script_logger_args(progname)
    cmd = "bash #{script} #{function} #{params} 2>&1 | "\
          "while read line; do datetime=$(date -u '+%Y-%m-%d %H:%M:%S'); "\
          "echo [\"$datetime\"]#{script_logger} \"$line\" >> "\
          "/var/log/browserstack/#{log_file}_#{@device_id}.log; done"
    log :info, "execute_cmd: #{cmd}"
    res = system(cmd)

    # Using this condition to log the failed system cmd when If res is false
    return if res

    log :warn, "Failed execute_cmd with cmd: #{cmd}"
  end

  def script_logger_args(progname='')
    "[#{progname}][#{@device_id}][#{@session_id}]"
  end

  def android_device
    @android_device ||= AndroidDevice.new(@device_id, self.class.to_s, @logger)
  end

  # Usage: This method is used when async_app_download_install is set to true for AA ustom_params.
  # Execution time: Less than 1ms.
  def kill_app_install_process
    pid_file = "#{BrowserStack::APP_INSTALL_PID_PREFIX_PATH}_#{@device_id}"
    return unless File.exist?(pid_file)

    pid = File.read(pid_file)
    system("kill #{pid}")
    log :info, "app install killed: #{pid}"
  end

  def kill_app_download_process
    pid_file = "#{BrowserStack::APP_DOWNLOAD_PID_PREFIX_PATH}_#{@device_id}"
    return unless File.exist?(pid_file)

    pid = File.read(pid_file)
    system("kill #{pid}")
    log :info, "app download killed: #{pid}"
  end

  def session_file_path
    "/tmp/apps/*/#{@session_id}_*.session"
  end

  def remove_session_file
    # This is done to remove session file from the apps dir
    return unless File.exist?(session_file_path)

    File.delete(session_file_path)
    log :info, "session file deleted: #{session_file_path}"
  end

  def remove_async_app_install_files
    pid_file = "#{BrowserStack::APP_INSTALL_PID_PREFIX_PATH}_#{@device_id}"
    app_install_log = "#{BrowserStack::APP_INSTALL_LOG_PREFIX_PATH}_#{@device_id}.log}"
    File.delete(pid_file) if File.exist?(pid_file)
    File.delete(app_install_log) if File.exist?(app_install_log)
    log :info, "async app install files deleted"
  end

  def stop_video_recording
    return unless File.exist?("/tmp/video_params_#{@device_id}")

    video_params = File.read("/tmp/video_params_#{@device_id}")
    log :info, "video_params: #{video_params}"

    session_id_from_video_params = video_params.split[1]
    if session_id_from_video_params != @session_id
      log :info, "session id mismatch from video_params, skipping video upload: #{session_id_from_video_params}"
      zombie_push('android',
                  "video-upload-sessionid-mismatch",
                  "session id from video params: #{session_id_from_video_params}",
                  '',
                  '',
                  @device,
                  @session_id)
      return
    end
    execute_cmd("#{File.basename(SELENIUM_SCRIPT)}_stop_video", "stop_video", SELENIUM_SCRIPT, "video", video_params )
  rescue StandardError => e
    log :warn, "Failed stop_video_recording: #{e.message} #{e.backtrace}"
  ensure
    File.delete("/tmp/video_params_#{@device_id}") if File.exist?("/tmp/video_params_#{@device_id}")
  end

  def handle_device_logs
    devicelogs_params = File.read("/tmp/devicelogs_params_#{@device_id}")
    log :info, "devicelogs_params: #{devicelogs_params}"
    execute_cmd(
      "#{File.basename(SELENIUM_SCRIPT)}_stop_video", "upload_devicelogs",
      SELENIUM_SCRIPT, "devicelogs", devicelogs_params
    )
    File.delete("/tmp/devicelogs_params_#{@device_id}")
  end

  def handle_crash_logs
    crashlogs_params = File.read("/tmp/crashlogs_params_#{@device_id}")
    log :info, "crashlogs_params: #{crashlogs_params}"
    execute_cmd(
      "#{File.basename(SELENIUM_SCRIPT)}_stop_video", "upload_crashlogs",
      SELENIUM_SCRIPT, "crashlogs", crashlogs_params
    )
  ensure
    File.delete("/tmp/crashlogs_params_#{@device_id}")
  end

  def handle_network_logs
    session_type = begin
      File.read("/tmp/sessionis_#{@device_id}").strip
    rescue StandardError
      ""
    end
    browser_name = begin
      File.read("/tmp/sessionbrowseris_#{@device_id}").strip
    rescue StandardError
      ""
    end
    networklogs_params = File.read("/tmp/networklogs_params_#{@device_id}")
    msg = "session_type: #{session_type}"\
        " - browser_name: #{browser_name}"\
        " - networklogs_params: #{networklogs_params}"
    log(:info, msg)
    if session_type != "selenium" || browser_name == "samsung"
      execute_cmd(
        "#{File.basename(SELENIUM_SCRIPT)}_upload_networklogs", "upload_networklogs",
        SELENIUM_SCRIPT, "networklogs", networklogs_params
      )
    else
      execute_cmd(
        "#{File.basename(SELENIUM_SCRIPT)}_upload_network_logs_har", "upload_network_logs_har",
        SELENIUM_SCRIPT, "networklogs", networklogs_params
      )
    end
    File.delete("/tmp/networklogs_params_#{@device_id}")
  end

  def handle_appium_logs
    appiumlogs_params = File.read("/tmp/appiumlogs_params_#{@device_id}")
    log :info, "appiumlogs_params: #{appiumlogs_params}"
    execute_cmd(
      "#{File.basename(SELENIUM_SCRIPT)}_upload_appiumlogs", "upload_appiumlogs", SELENIUM_SCRIPT,
      "appiumlogs", appiumlogs_params
    )
    File.delete("/tmp/appiumlogs_params_#{@device_id}")
  end

  def handle_playwright_logs
    playwrightlogs_params = File.read("/tmp/playwrightlogs_params_#{@device_id}")
    log :info, "playwrightlogs_params: #{playwrightlogs_params}"
    execute_cmd(
      "#{File.basename(SELENIUM_SCRIPT)}_upload_playwrightlogs", "upload_playwrightlogs", SELENIUM_SCRIPT,
      "playwrightlogs", playwrightlogs_params
    )
    File.delete("/tmp/playwrightlogs_params_#{@device_id}")
  end

  def handle_screenshot_logs
    screenshot_params = File.read("/tmp/screenshotlogs_params_#{@device_id}")
    log :info, "handle_screenshot_logs: #{screenshot_params}"
    execute_cmd(
      "#{File.basename(SELENIUM_SCRIPT)}_upload_google_login_screenshot",
      "upload_google_login_screenshot", SELENIUM_SCRIPT, "google_login_screenshot", screenshot_params
    )
    File.delete("/tmp/screenshotlogs_params_#{@device_id}")
  end

  def handle_app_live_logs
    if File.exist?("/tmp/pm_tools_used_#{@device_id}")
      log :info, "uploading app live logs"
      execute_cmd(
        "#{File.basename(SELENIUM_SCRIPT)}_upload_app_live_logs", "upload_app_live_logs",
        APP_LIVE_SCRIPT, "devicelogs", @device_id
      )
      File.delete("/tmp/pm_tools_used_#{@device_id}")
    end
    File.delete("/tmp/app_live_logs_params_#{@device_id}")
  end

  def handle_logs
    handle_device_logs if File.exist?("/tmp/devicelogs_params_#{@device_id}")
    handle_network_logs if File.exist?("/tmp/networklogs_params_#{@device_id}")
    handle_appium_logs if File.exist?("/tmp/appiumlogs_params_#{@device_id}")
    handle_playwright_logs if File.exist?("/tmp/playwrightlogs_params_#{@device_id}")
    handle_screenshot_logs if File.exist?("/tmp/screenshotlogs_params_#{@device_id}")
    handle_app_live_logs if File.exist?("/tmp/app_live_logs_params_#{@device_id}")
    handle_crash_logs if File.exist?("/tmp/crashlogs_params_#{@device_id}")
    handle_mitm_logs if File.exist?("/tmp/run_mitm_#{@device_id}.sh")
  end

  def handle_mitm_logs
    retries = 2
    begin
      File.delete("/tmp/run_mitm_#{@device_id}.sh")
    rescue StandardError => e
      log :info, "Failed to delete run_mitm script: #{e.message}"
      retry if (retries -= 1) >= 0
    end
  end

  def log_crypto_mining_detection
    log :info, "Sending crypto mining instrumentaion to zombie: #{@genre}"
    CryptoMiningDetectionHelper.new(@device_id, @logger, @session_id, @genre).instrument_possible_crypto_mining
  end

  def kill_processes
    grep_v_items = if File.exist?("#{BS_HOME}/files_to_be_processed/files_to_upload/async_#{@session_id}")
                     %W[grep tail cleanup_#{@device_id}.log cleanup_flow_#{@device_id}.log bshelper.sh upload_to_s3.rb
                        android_influxdb_client.rb push_to_zombie.rb har_dump.py qtfaststart MP4Box ffmpeg push_to_eds
                        screenrecord video_rec scrcpy].join("\\|")
                   else
                     %W[grep tail cleanup_#{@device_id}.log cleanup_flow_#{@device_id}.log
                        bshelper.sh qtfaststart MP4Box ffmpeg push_to_eds screenrecord video_rec
                        scrcpy].join("\\|")
                   end
    cmd = "ps aux"\
        " | grep #{@device_id}"\
        " | grep -v \"#{grep_v_items}\""\
        " | awk '{print $2}'"\
        " | xargs sudo kill -9"
    log :info, "kill_processes cmd #{cmd}"
    system(cmd)
  end

  def check_device_on_adb
    devices_on_adb = adb.devices
    if devices_on_adb.include? @device_id
      log :info, "Device on ADB"
    else
      # Need to move this log to log_cleanup_flow
      log :info, "DIE: Device not on ADB!"
      FileUtils.touch("#{STATE_FILES_DIR}/unclean_off_adb_#{@device_id}")
      raise "Device not on ADB"
    end
  end

  def check_and_install_ui_automation_apps
    log :info, "Checking and installing UI automation apps"
    ui_automation_manager = UIAutomationAppsManager.new(@device_id, @logger)
    ui_automation_manager.ensure_install
    log :info, "Installed UI automation apps"
  end

  def check_and_install_browserstack_app
    log :info, "Checking and installing BrowserStack App"
    browserstack_app_manager = BrowserStackAppManager.new(@device_id, @logger)
    browserstack_app_manager.ensure_install
    log :info, "Installed UI BrowserStack App"
  end

  def check_frozen
    raise "manual fix: Unable to restore frozen device" unless android_device.ensure_not_frozen

    log :info, "Device not frozen"
  end

  def check_mid_session_off_adb_errors
    off_adb_counter_file = "#{STATE_FILES_DIR}/off_adb_counter_#{@device_id}"
    return unless File.exist?(off_adb_counter_file)

    off_adb_count = begin
      File.read(off_adb_counter_file).strip.to_i
    rescue StandardError
      0
    end

    raise "manual fix: Unstable adb, please check cable and port" if off_adb_count > 20
  end

  def need_manual_fix_sim_tray?
    return true if @device_state.device_logger_detected_check_sim_tray_file_present?

    false
  end

  def check_sim_tray_popup
    raise "manual fix required: Sim tray popup, please reinsert the sim tray appropriately" if need_manual_fix_sim_tray?

  end

  def check_device_logger_reliability
    return if File.exist?("#{STATE_FILES_DIR}/manual_cleanupdone_#{@device_id}")

    log :info, "Checking Device Logger reliability"
    zombie_data = {}
    zombie_data["last_session_id"] = @session_id
    zombie_data["last_session_genre"] = @genre
    zombie_data["device_logger_reliable"] = false
    unless File.exist?("#{STATE_FILES_DIR}/device_logger_pid_#{@device_id}")
      log :info, "Device logger state file not found"
      zombie_push('android', 'dl-pid-file-absent', 'device logger pid file absent in host', '', zombie_data.to_json,
                  @device_id, '', '')
      return
    end

    pid_from_file = File.read("#{STATE_FILES_DIR}/device_logger_pid_#{@device_id}").strip.to_s
    session_end_file = "#{STATE_FILES_DIR}/device_logger_session_end_pid_#{@device_id}"
    dl_pid_at_session_end = File.exist?(session_end_file) ? File.read(session_end_file).strip.to_s : ""
    log :info, "Device Logger PID At Session End : #{dl_pid_at_session_end}"
    if dl_pid_at_session_end.to_i == pid_from_file.to_i
      log :info, "Device Logger reliable"
      zombie_data["device_logger_reliable"] = true
    else
      log :info, "Device Logger unreliable"
      # Touch device logger unreliable file
      FileUtils.touch("#{STATE_FILES_DIR}/device_logger_unreliable_#{@device_id}")
    end

    log :info, "Pushing Device logger stability to zombies"
    zombie_push('android', 'android-device-logger-reliability', 'dl pid matched', '', zombie_data.to_json, @device_id,
                @session_id, '')
    FileUtils.rm_f("#{STATE_FILES_DIR}/device_logger_pid_#{@device_id}")
    FileUtils.rm_f("#{STATE_FILES_DIR}/device_logger_session_end_pid_#{@device_id}")
  end

  def check_device_stability
    BrowserStackUtils::LogCleanupFlow.log(@device_id, Time.now.to_i,
                                          "check_device_stability started #{@device_id} #{@session_id}")
    #kill_processes - currently this is running via bash
    check_device_on_adb
    check_frozen
    check_mid_session_off_adb_errors
    check_device_logger_reliability
    check_and_install_ui_automation_apps
    check_and_install_browserstack_app
    check_sim_tray_popup
  rescue StandardError => e
    log :warn, "Failed check_device_stability: #{e.message} #{e.backtrace}"
    ExitFile.write(e.message)
    raise
  end

  def handle_and_upload_logs
    BrowserStackUtils::LogCleanupFlow.log(@device_id, Time.now.to_i,
                                          "handle_and_upload_logs started #{@device_id} #{@session_id}")
    kill_app_download_process
    kill_app_install_process
    remove_async_app_install_files
    remove_session_file
    stop_video_recording
    handle_logs
  rescue StandardError => e
    log :warn, "Failed handle_and_upload_logs: #{e.message} #{e.backtrace}"
  end

  def stop_fullcone_nat_script
    log :info, "Stopping FullconeNat script for device: #{@device_id}"

    # Get the device's tun interface IP
    source = "pre_cleanup.rb - stop_fullcone_nat_script"
    device_obj = AndroidDevice.new(@device_id, source, @logger)
    tun_interface_ip = device_obj.vpn_ip

    if tun_interface_ip.nil? || tun_interface_ip.empty?
      log :warn, "No tun_interface_ip found for device: #{@device_id}, skipping FullconeNat stop"
      return
    end

    log :info, "Found tun_interface_ip: #{tun_interface_ip} for device: #{@device_id}"

    # Get the manager instance and stop the specific FullconeNat service
    manager = FullconeNATManager.instance
    fullcone_nat = manager.get_or_create(tun_interface_ip)

    if fullcone_nat.running?
      log :info, "Stopping running FullconeNat for tun_interface_ip: #{tun_interface_ip}"
      success = fullcone_nat.stop
      if success
        log :info, "Successfully stopped FullconeNat for device: #{@device_id}"
      else
        log :warn, "Failed to stop FullconeNat for device: #{@device_id}"
      end
    else
      log :info, "FullconeNat not running for tun_interface_ip: #{tun_interface_ip}"
    end

  rescue StandardError => e
    log :error, "Error stopping FullconeNat script: #{e.message} #{e.backtrace}"
  end

  def stop_monitoring_thread
    if BrowserActivityMonitoring.running?(@device_id)
      File.delete(BrowserActivityMonitoring.start_file(@device_id))
      log :info, "/cleanup BrowserActivityMonitoring removing start file for #{@session_id}"
    end
  rescue StandardError => e
    log :error, "/cleanup BrowserActivityMonitoring removing start file exception: #{e.message}"
  end

  def stop_session_save_polling
    if SessionSavePolling.running?(@device_id)
      File.delete(SessionSavePolling.start_file(@device_id))
      log :info, "/cleanup [SessionSavePolling] removing start file for #{@session_id}"
    end
    local_file_path = "/usr/local/.browserstack/state_files/#{@device_id}_cookie_data_from_s3.json"
    if File.exist?(local_file_path)
      File.delete(local_file_path)
      log :info, "/cleanup [SessionSavePolling] Deleted the cookies file from local path"
    end
  rescue StandardError => e
    log :error, "/cleanup [SessionSavePolling] removing start file exception: #{e.message}"
  end

  def enable_play_services
    # On auto upgrade, play service is not starting properly, causing issues in the next sessions
    # enabling the gms in every cleanup, to make sure its in good state for next sessions
    # https://browserstack.atlassian.net/browse/MOBCR-3075
    adb.shell("pm enable com.google.android.gms", timeout: 5)
  rescue StandardError => e
    log :error, "Something went wrong while enabling GMS: #{e.message}"
  end

  def run
    handle_and_upload_logs
    log_crypto_mining_detection
    stop_fullcone_nat_script

    # this is a safe check
    # in case it failed in /stop, trying in /cleanup
    stop_monitoring_thread
    stop_session_save_polling
    enable_play_services
  end

  private

  def log(level, msg)
    params = { component: self.class.to_s, device: @device_id, session: @session_id }
    if @logger.instance_of?(Logger)
      @logger.send(level.to_sym, msg)
    else
      @logger.send(level.to_sym, msg, params)
    end
  end

  def adb
    @adb ||= AndroidToolkit::ADB.new(udid: @device_id, path: BrowserStack::ADB)
  end

end

if $PROGRAM_NAME == __FILE__
  command = ARGV[0].to_s.strip.downcase.to_sym
  device_id = ARGV[1].to_s.strip
  session_id = ARGV[2].to_s.strip
  genre = ARGV[3].to_s.strip || nil
  helper = PreCleanup.new(device_id, session_id, Logger.new($stdout), {}, genre)
  helper.send(command)
end
