# frozen_string_literal: true

require 'yaml'

module BrowserStack # rubocop:todo Metrics/ModuleLength
  # TODO: Proof of concept.
  # Most/all of these could be defined at a higher level than here

  BS_DIR = '/usr/local/.browserstack'
  ENV_FILE = "#{BS_DIR}/env"
  PATH = "#{BS_DIR}/mobile/android"
  CONFIG_DIR = "#{BS_DIR}/config"
  MOBILE_CONFIG = "#{BS_DIR}/mobile/config"
  MOBILE_CUSTOM_DEVICES = "#{MOBILE_CONFIG}/custom_devices"
  STATE_FILES_DIR = "#{BS_DIR}/state_files"
  DEPS_DIR = "#{BS_DIR}/deps"
  WEBRTC_DIR = "#{BS_DIR}/webrtc"
  LOGGING_DIR = '/var/log/browserstack'
  MOBILE_COMMON_HOME = "#{BS_DIR}/mobile-common"
  USB_VPN_DIR = "#{BS_DIR}/usbVpn/"
  TMP_DIR = "/tmp"
  SESSION_DIR = "/data/local/tmp/session"
  TEMP_CLEANUP_FILE = "/data/local/tmp/fullcleanup.txt"
  COMMON = "#{BS_DIR}/mobile/common"
  CUSTOM_CONFIG = "#{CONFIG_DIR}/custom_device_configs"
  SYSTEMD_SERVICES_PATH = "/etc/systemd/system"
  PRIVOXY_SERVICE_TEMPLATE = "#{PATH}/proxies/privoxy/privoxy_service.erb"
  PRIVOXY_DIR = "#{BS_DIR}/privoxy"
  INSECURE_WS_PROXY_DEVICES_CONF = "#{BS_DIR}/mobile/config/insecure_websocket_mapping.json"
  IS_CANARY_FILE = "#{CONFIG_DIR}/is_canary"
  ADB_FORWARDER = "adb_forwarder"
  ADB_FORWARDER_DIR = "#{BS_DIR}/#{ADB_FORWARDER}"
  ADB_FORWARDER_SERVICE_TEMPLATE = "#{PATH}/proxies/adb_forwarder/adb_forwarder_service.erb"

  BUSYBOX = "/data/local/tmp/busybox"
  BUNDLE = "/home/<USER>/bin/bundle"
  LIVE_SCRIPTS_DIR = "#{BS_DIR}/mobile/android/live/scripts"
  MODEL_DATABASE_SCRIPT = "#{BS_DIR}/mobile/android/scripts/model_database"

  APP_LIVE = 'app_live'
  LIVE = 'live'
  APP_ACCESSIBILITY = 'app_accessibility'
  APP_ACCESSIBILITY_TEAM = 'app_a11y_dev'

  MAX_TERMINAL_BLOCK_TIME = 4 * 60 * 60 # 4 hours

  # interactions-server version needs to be updated here as well
  # android/driver_actions.sh
  # android/live_common.sh
  INTERACTIONS_SERVER_VERSION = "1.6.0"

  WEBRTC_CONTENTS_LIST = [
    "InputInjector.jar",
    "busybox",
    "start_binary.sh",
    "interactions_server",
    "interactions-server-#{INTERACTIONS_SERVER_VERSION}",
    "minicap-debug.apk"
  ].freeze

  UCBROWSER_FLAGS = "#{BS_DIR}/mobile/android/helpers/uc_flags_counter.sp"

  CONFIG_JSON_FILE = "#{CONFIG_DIR}/config.json"
  USB_INFO_FILE = "#{CONFIG_DIR}/usb_info.json"

  MOBILE_POLL_LOG_FILE = "#{LOGGING_DIR}/mobile_poll.log"

  DEVICE_OFFLINE_LOCK_FILE = "#{TMP_DIR}/device_offline_lock"

  DEPS_CHROME_DIR = "#{DEPS_DIR}/chrome"

  RTCAPP_MANIFEST_FILE = "#{BS_DIR}/mobile/android/live/streaming/webrtc_app/app/src/main/AndroidManifest.xml"
  RTCAPP_2_MANIFEST_FILE = "#{BS_DIR}/mobile/android/live/streaming/webrtc_app_2/app/src/main/AndroidManifest.xml"
  RTCAPP_2_PACKAGE_NAME = "fr.pchab.AndroidRTC2"
  RTCAPP_PACKAGE_NAME = 'fr.pchab.AndroidRTC'
  RTCAPP_2_NON_SUPPORTED_DEVICES = ['SM-A115M'].freeze
  # Total of 5 devices will not support work with MediaProjection mentioned in
  # https://browserstack.atlassian.net/wiki/spaces/ENG/pages/3905684475/TechSpec+-+MediaProjection+for+video+recording#case-1
  # But we are not installing only on Samsung A11 to keep in sync with LSE's configuration
  MEDIA_PROJECTION_LAST_TEST_CONSTANT_INCREASE = 4

  LIVE_SCRIPT = "#{PATH}/live/scripts/live_actions.sh"
  DRIVER_ACTIONS = "#{PATH}/driver_actions.sh"
  INTERACTION_CONFIG_FILE = "#{PATH}/live/interaction_server/device_config.json"
  GET_LAST_URL_SCRIPT_PATH = "/usr/local/.browserstack/mobile/android/live/scripts/getLastUrl.sh"

  DEFAULT_STREAM_WIDTH = 384

  ADB = ENV["ADB"].freeze || '/usr/local/bin/adb'
  FFMPEG = ENV["FFMPEG"].freeze || 'ffmpeg'
  PSTREE = ENV["PSTREE"].freeze || 'pstree'
  WS_RECONNECT_PROXY = ENV["WS_RECONNECT_PROXY"].freeze
  INSECURE_WEBSOCKET_PROXY = ENV["INSECURE_WEBSOCKET_PROXY"].freeze
  SOCAT = ENV["SOCAT"].freeze
  ADB_PORT = 5037
  ADB_PROXY_PORT = 5038
  ADB_PROXY_PATH = "#{PATH}/helpers/adb_proxy.rb"

  PROD_RAILS_ENDPOINT = 'www.browserstack.com'
  PRODUCTION_SITE = 'browserstack.com'

  SEND_NETWORK_LOGS_TO_EDS = true

  S3_IA_MIN_FILE_SIZE_IN_KB = 128

  CLEANUP_ENDPOINT = 'http://localhost:45680/cleanup'

  COMPONENT_VERSION_CONFIG_PATH = "#{MOBILE_CONFIG}/component_versions.yml"

  FEATURE_FLAGS_CONFIG_PATH = "#{MOBILE_CONFIG}/feature_flags.json"

  UI_AUTOMATION_RULES_CONFIG = "#{PATH}/static/ui_automation_rules_config.json"
  UI_AUTOMATION_APP_PACKAGE_NAME = 'com.browserstack.uiautomation'
  UI_AUTOMATION_TEST_APP_PACKAGE_NAME = 'com.browserstack.uiautomation.test'
  TRICHROME_LIBRARY_PACKAGE_NAME = 'com.google.android.trichromelibrary'

  SAMSUNG_ACCOUNT_PACKAGE_NAME = 'com.osp.app.signin'
  PLAY_SERVICES_PACKAGE_NAME = 'com.google.android.gms'
  PLAY_SERVICES_REINSTALL_REQUIRED = 'play_services_reinstall_required'
  PLAY_STORE_PACKAGE_NAME = 'com.android.vending'
  PLAY_STORE_LAUNCHER_ACTIVITY_NAME = 'com.google.android.finsky.activities.MainActivity'
  WEBVIEW_PACKAGE_NAME = 'com.google.android.webview'
  BROWSERSTACK_APP_PACKAGE_NAME = 'com.android.browserstack'
  PDFVIEWER_APP_PACKAGE_NAME = 'com.google.android.apps.pdfviewer'
  VPN_REVERSE_TETHER_APP_PACKAGE_NAME = 'com.google.android.vpntether'
  GNIREHTET_APP_PACKAGE_NAME = 'com.genymobile.gnirehtet'
  BSTACK_REVERSE_TETHER_PACKAGE_NAME = 'com.browserstack.reversetether'
  MAGISK_APP_PACKAGE_NAME = 'com.topjohnwu.magisk'
  CAMERA_CHECK_PACKAGE_NAME = 'com.browserstack.canisee'
  INPUT_INJECTOR_PACKAGE_NAME = 'com.browserstack.inputinjector'
  INPUT_INJECTOR_TEST_PACKAGE_NAME = 'com.browserstack.inputinjector.test'
  TEST_ORCHESTRATOR_APP_PACKAGE_NAME = 'androidx.test.orchestrator'
  BROWSERSTACK_DEVICE_OWNER_PACKAGE_NAME = 'com.browserstack.deviceowner'
  BROWSERSTACK_WATCHER_PACKAGE_NAME = 'com.browserstack.watcher'
  TEST_SERVICES_APP_PACKAGE_NAME = 'androidx.test.services'
  GOOGLEMAPS_PACKAGE_NAME = 'com.google.android.apps.maps'
  ORCHESTRATOR_RUNNER = 'androidx.test.orchestrator/.AndroidTestOrchestrator'
  ORCHESTRATOR_SHELL_EXECUTOR =
    'CLASSPATH=$(pm path androidx.test.services) app_process / androidx.test.services.shellexecutor.ShellMain '
  FACEBOOK_PACKAGE_NAME = 'com.facebook.katana'

  KNOWN_PLAYSTORE_INSTALLED_APPS = ['com.google.android.instantapps.supervisor',
                                    'com.google.android.googlequicksearchbox'].freeze

  # Should be: /usr/local/.browserstack/android-sdk/build-tools/29.0.2/dexdump
  DEXDUMP = Dir["#{BrowserStack::BS_DIR}/android-sdk/build-tools/*/dexdump"].max

  # Should be: /usr/local/.browserstack/android-sdk/build-tools/29.0.2/aapt
  AAPT = Dir["#{BrowserStack::BS_DIR}/android-sdk/build-tools/*/aapt"].max

  # Should be: /usr/local/.browserstack/android-sdk/build-tools/29.0.2/aapt2
  AAPT2 = Dir["#{BrowserStack::BS_DIR}/android-sdk/build-tools/*/aapt2"].max
  DATABASE_PATH = "#{BS_DIR}/mobile_database.sqlite"

  CACHE_DB_PATH = "#{BS_DIR}/cache.sqlite"

  BROWSERSTACK_ERROR_STRING = 'browserstack_error'
  USER_ERROR_STRING = 'user_error'
  CUSTOM_CONTACTS_PRELOAD_FAILED = 'custom_contacts_preload_failed'
  CUSTOM_PROP = 'debug.test'
  MAESTRO_PROP = 'debug.maestro.sessionId'

  # Ideally, we should be doing this for all appium versions
  STOP_UIAUTOMATOR_PORT_FORWARD_APPIUM_VERSION = %w[1.22.0 2.0.0 2.0.1 2.4.1 2.6.0 2.12.1 2.15.0 2.18.0].freeze

  ESPRESSO_SESSION_TRIGGER_TIME = 5
  ESPRESSO_DEVICE_NOT_FOUND = 'Device not found.'
  ESPRESSO_DEVICE_OFFLINE = "Device ADB Offline."
  ESPRESSO_ROOT_VIEW_HIERARCHY = 'Waited for the root of the view hierarchy'\
  ' to have window focus and not request layout for 10 seconds.'
  ESPRESSO_DEVICE_NOT_UNLOCKED = 'Device is not unlocked.'
  ESPRESSO_INSTRUMENT_COMMAND_STALLED = 'Instrumentation stalled'
  ESPRESSO_INSTRUMENTATION_ABORTED = 'Instrumentation aborted'
  ESPRESSO_INSTRUMENTATION_EMPTY = 'Instrumentation logs empty.'
  ESPRESSO_DEVICE_AM_CRASH = "Device am crashed."
  ESPRESSO_SOCKET_TIMEOUT = "java.net.(SocketTimeoutException|ConnectException): (failed|Failed) to connect to"
  ESPRESSO_DEVICE_OFF_ADB_FILE_PREFIX = "#{TMP_DIR}/device_off_adb"
  CONTACTS_APP_ACCESS = "contacts_app_access"

  # Contains observability related data for espresso sessions
  TEMP_TEST_OBSERVABILITY_FILE = "/data/local/tmp/test_observability.json"
  # Contains SDK logs
  TEMP_SDK_LOG_FILE = "/data/local/tmp/sdk.log"

  # Working directory for watcher
  WATCHER_WORKING_DIR = "/data/user/0/#{BROWSERSTACK_WATCHER_PACKAGE_NAME}/files"
  CONTINUOUS_SCANNING_STATE_FILE = "continuous_scanning_session.txt"

  DEVICE_REPORTS_DIR = "/sdcard/Download/assets/reports"
  DEVICE_SESSION_ASSETS_DIR = "/sdcard/Download/assets/session_data"
  DRY_RUN_OPTIONS = ["tags", "features", "name", "dry_run"].freeze
  CUCUMBER_DEFAULT_FLOW_OPTIONS = ["name", "plugins"].freeze
  SRI_OPTIONS = ["tags", "features", "name", "plugins"].freeze
  CUCUMBER_REPORTS_MAP = {
    "html" => {
      "dir" => "html",
      "extension" => ""
    },
    "pretty" => {
      "dir" => "pretty",
      "extension" => ".pretty"
    },
    "json" => {
      "dir" => "json",
      "extension" => ".json"
    },
    "junit" => {
      "dir" => "xml",
      "extension" => ".xml"
    }
  }.freeze
  DEVICE_COVERAGE_FILE_FOLDER = "/sdcard/Download/reports"
  COVERAGE_FILE_NAME = "coverage.ec"

  INSTALL_PHASE = {
    name: "install_phase", # Please ensure this is unique.
    log_file_prefix: "install_phase",
    timeout: 1800 # in seconds
  }.freeze

  APPS_DOWNLOAD_FOLDER = '/tmp/apps'
  APP_DOWNLOAD_PID_PREFIX_PATH = '/tmp/app_download_pid'
  APP_INSTALL_PID_PREFIX_PATH = '/tmp/app_install_pid'
  APP_INSTALL_LOG_PREFIX_PATH = '/tmp/app_install_log'
  APP_DOWNLOAD_HEADER_FILE_PREFIX_PATH = '/tmp/app_download_header_file'
  SUB_REGION_APP_CACHING_PROXY_HOST = "lb-app-caching-proxy.service.prod.mobile.browserstack.com:45901"
  SUBREGION_CACHING_RELATED_INSTALL_ERRORS = [/INSTALL_PARSE_FAILED_NO_CERTIFICATES/].freeze

  APPIUM_CONFIG_FILE = "#{MOBILE_CONFIG}/default_appium_version"

  ASYNC_UPLOAD_REQUEST_DIR = "/usr/local/.browserstack/files_to_be_processed/files_to_upload"

  RESERVED_FLOW_SESSION_MAX_TIME_IN_MINUTES = 65

  LIMITED_NETWORK_CAP = {
    max_download: {
      usb: "40Mbit",
      wifi: "40Mbit"
    },
    max_upload: {
      usb: "40Mbit",
      wifi: "40Mbit"
    },
    network_latency: "0",
    network_pk_loss: "0",
    network_bw_dwld: {
      usb: "40000", # in kbps
      wifi: "40000"  # in kbps
    },
    network_bw_upld: {
      usb: "40000", # in kbps
      wifi: "40000"  # in kbps
    }
  }.freeze

  ALLOW_LIMITED_NETWORK_FILE = "/tmp/allow_limited_network"

  ROUTER_HEALTHCHECKER = {
    script_file_host: "#{BS_DIR}/mobile/android/scripts/router_healthcheck.sh",
    # SCRIPT_FILE_HOST is pushed to this path in the device
    script_file_device: "/data/local/tmp/router_healthcheck.sh",
    script_log_file: "/sdcard/router_connection_status", # Present in the device
    busybox: "/data/local/tmp/busybox",
    healthcheck_interval: 60, #second
    session_deferred_start_time: 20, # seconds, will start healthchecker after this delay
    minimum_os_version: Gem::Version.new(6.0),
    # Number of internet down events to receive, which will qualify a session as having network issues
    internet_down_threshold: 5
  }.freeze

  INTERNET_HEALTHCHECKER = {
    script_log_file: "/sdcard/internet_status", # Present in the device
    # Failure percentage of usb internet down events to receive,
    # which will qualify a session as having usb network issues
    usb_internet_down_threshold: 0,
    # Failure percentage of wifi internet down events to receive,
    # which will qualify a session as having wifi network issues
    wifi_internet_down_threshold: 20,
    connection_types: ['usb', 'wifi']
  }.freeze

  WIFI_CHECKER_ON_HOST = "#{BS_DIR}/mobile/android/scripts/check_wifi_connection.sh"
  WIFI_CHECKER_ON_DEVICE = "/data/local/tmp/check_wifi_connection.sh"

  ZOMBIE_STAGING_URL = "pager.bsstag.com"
  CLS_STAGING_URL = "logs.bsstag.com"
  CLS_STAGING_PORT = 41234

  FREETOOLS_WORKSPACE = "#{BS_DIR}/free_tools"
  MOCK_LOCATION_USE_FORK_GENRE = ['live_testing', 'app_live_testing'].freeze
  DATA_LOCAL_TMP = '/data/local/tmp'

  SCREENSHOT_INSTRUMENTATION_PREFIX = "screenshot_instrumentation_"
  SCREENSHOT_METRICS_THRESHOLD = 25 * 60 # 25 minutes
  MITMPROXY_CA_CERT_FILE_HASH = "efb15d7d.0"
  MITMPROXT_CA_CERT_FILE_MD5SUM = "d5d9f142ec09c0c05c6db467acae8b23"
  MITMPROXY_CA_CERT_LOCATION_SOURCE = "/home/<USER>/.mitmproxy/mitmproxy-ca-cert.cer"
  MITMPROXY_CA_CERT_TMP_LOCATION = "#{DATA_LOCAL_TMP}/#{MITMPROXY_CA_CERT_FILE_HASH}"
  CHROME_TMP_FLAG_FILE = "#{DATA_LOCAL_TMP}/chrome-command-line"

  # Can find the following soft symlink: `etc -> /system/etc`
  CA_CERT_LOCATIONS = [
    "/system/etc/security/cacerts/#{MITMPROXY_CA_CERT_FILE_HASH}",
    "/system_root/etc/security/cacerts/#{MITMPROXY_CA_CERT_FILE_HASH}",
    "/data/misc/user/0/cacerts-added/#{MITMPROXY_CA_CERT_FILE_HASH}"
  ].freeze

  ONEPLUS_7_MODELS = ["GM1900", "GM1901", "GM1905"].freeze
  ONEPLUS_7T_MODELS = ["HD1900", "HD1901", "HD1903", "HD1905", "HD1907"].freeze
  ONEPLUS_6_MODELS = ["ONEPLUS A6010", "ONEPLUS A6013"].freeze

  # List of models for which we will be using browser config for updates and installations
  BROWSER_CONFIG_MODELS = ["vivo 1935", "CPH2487"].freeze

  # AppLive udpate device logs file path
  APP_LIVE_UPDATE_DEVICE_LOGS_DIR = "#{TMP_DIR}/AL_minimum_devicelog_level"
  APP_LIVE_UPDATE_DEVICE_LOGS_PATH = "#{APP_LIVE_UPDATE_DEVICE_LOGS_DIR}/%s"

  # Passcode
  DEVICE_PASSCODE = '1234'
  DEVICE_PASSCODE_MIN_OS = "8"
  DEVICE_PASSCODE_OS_NOT_SUPPORTED = "OS version should be minimum of 8.0"

  #sim flow constants
  SIM_COMPARISON_CHECK_INTERVAL = 60

  # Device orientation
  DEVICE_ORIENTATION_MIN_OS = "10"

  # Fix Rotation
  FIX_ROTATION_MIN_OS = "10"
  FIX_ROTATION_OS_NOT_SUPPORTED = "OS version should be minimum of 10.0"

  # Fix Rotation
  DEVICE_SENSORS_MIN_OS = "10"
  DEVICE_SENSORS_OS_NOT_SUPPORTED = "OS version should be minimum of 10.0"

  # Fix Rotation
  TWELWE_HOUR_FORMAT_MIN_OS = "6"
  TWELWE_HOUR_FORMAT_OS_NOT_SUPPORTED = "OS version should be minimum of 6.0"

  # Android Public SIM
  PUBLIC_SIM_MIN_OS = "10"
  PUBLIC_SIM_OS_NOT_SUPPORTED = "OS version should be minimum of 10.0"

  # Check if these services are running before marking device ready for ADB install commands
  ANDROID_SERVICES_REQUIRED_TO_BE_RUNNING = ["android.content.pm.IPackageManager"].freeze

  INJECTION_MEDIA_DIR = '/tmp/injection_media'
  DEVICE_INJECTION_MEDIA_BASE_DIR = '/storage/emulated/0/Download'
  DEVICE_CAMERA_MEDIA_INJECTION_DIRECTORY_PATH = "/data/local/tmp"
  DEVICE_CAMERA_IMAGE_INJECTION_PATH = "#{DEVICE_CAMERA_MEDIA_INJECTION_DIRECTORY_PATH}/BrowserstackMockImage.png"
  DEVICE_CAMERA_VIDEO_INJECTION_PATH = "#{DEVICE_CAMERA_MEDIA_INJECTION_DIRECTORY_PATH}/BrowserstackMockVideo.mp4"
  PATCHED_APP_LOGS_DEVICE_PATH = '/data/local/tmp/patching.log'
  PATCHED_APP_LOGS_PATH = "#{LOGGING_DIR}/app_patching_%s.log"
  TMP_PATCHED_APP_LOGS_PATH = "#{TMP_DIR}/app_patching_%s.log"
  APP_PATCH_TYPES = ["biometrics", "camera", "cameraPreviewLayer", "videoInjection", "instrumented",\
                     "mobileData"].freeze
  ENABLE_CHOOSER_INTENT_SUPPORT_FILE = "/data/local/tmp/enable_chooser_intent_support.txt"
  ENABLE_CAMERA_PREVIEW_LAYER_FILE = "#{DEVICE_CAMERA_MEDIA_INJECTION_DIRECTORY_PATH}/enable_camera_preview_layer.txt"
  ENABLE_VIDEO_INJECTION_FILE = "#{DEVICE_CAMERA_MEDIA_INJECTION_DIRECTORY_PATH}/enable_video_injection.txt"
  ENABLE_BIOMETRIC_FRIDA = "/data/local/tmp/biometric"
  ENABLE_CAMERA_FRIDA = "/data/local/tmp/camera"
  ENABLE_MULTIPLE_BIOMETRIC_FAILURES = "/data/local/tmp/multiple_biometric_failures"
  ENABLE_NETWORK_LOGS_FRIDA = "/data/local/tmp/network_logs"
  FILE_INJECTION_SUCCESS = "FILE_INJECTION_SUCCESS"
  FILE_INJECTION_FAILED = "FILE_INJECTION_FAILED"
  TEMP_OPENED_BROWSERS_FILE = '/data/local/tmp/opened_browsers.txt'
  OPENED_BROWSERS_FILE = "#{BrowserStack::STATE_FILES_DIR}/opened_browsers_device_id"
  # Add params here to avoid getting it logged in logcat
  BLACKLISTED_RTC_PARAMS = ['video_aws_keys', :video_aws_keys,
                            'video_aws_secret', :video_aws_secret,
                            'video_aws_bucket', :video_aws_bucket,
                            'video_aws_region', :video_aws_region,
                            'video_file', :video_file,
                            'devicelogs_aws_key', :devicelogs_aws_key,
                            'devicelogs_aws_secret', :devicelogs_aws_secret,
                            'devicelogs_aws_bucket', :devicelogs_aws_bucket,
                            'devicelogs_aws_region', :devicelogs_aws_region,
                            'networklogs_aws_key', :networklogs_aws_key,
                            'networklogs_aws_secret', :networklogs_aws_secret,
                            'networklogs_aws_bucket', :networklogs_aws_bucket,
                            'networklogs_aws_region', :networklogs_aws_region,
                            'appiumlogs_aws_key', :appiumlogs_aws_key,
                            'appiumlogs_aws_secret', :appiumlogs_aws_secret,
                            'appiumlogs_aws_bucket', :appiumlogs_aws_bucket,
                            'appiumlogs_aws_region', :appiumlogs_aws_region,
                            'crashlogs_aws_key', :crashlogs_aws_key,
                            'crashlogs_aws_secret', :crashlogs_aws_secret,
                            'crashlogs_aws_bucket', :crashlogs_aws_bucket,
                            'crashlogs_aws_region', :crashlogs_aws_region,
                            's3_app_url', :s3_app_url,
                            'host', :host,
                            'stats_aws_key', :stats_aws_key,
                            'stats_aws_secret', :stats_aws_secret,
                            'stats_aws_bucket', :stats_aws_bucket,
                            'stats_aws_region', :stats_aws_region,
                            'stats_aws_storage_class', :stats_aws_storage_class].freeze

  ZIP = ".zip"
  # Google Pay
  GOOGLE_PAY = "google_pay"
  GOOGLE_PAY_TAG = "[Google Pay]"
  GOOGLE_PLAY_SERVICES_PACKAGE = 'com.google.android.gms'
  GOOGLE_PAY_PUSHER_SUCCESS_MESSAGE = "GOOGLE_PAY_SUCCESS"
  GOOGLE_PAY_PUSHER_FAILURE_MESSAGE = "GOOGLE_PAY_FAILURE"
  GOOGLE_PAY_DEVICES = [
    "1B121FDEE004LK", "23031FDEE000BK", "5b34b9d9", "00ebcfca", "23021FDEE0055S", "23021FDEE0052D",
    "23031FDEE002E7", "9A311FFBA00950", "9B041FFBA008KE", "99231FFBA004T8", "66a402b3",
    "61d70c31"
  ].freeze

  # Audio Injection
  AUDIO_INJECTION = 'audio_injection'
  AUDIO_INJECTION_TAG = "[#{AUDIO_INJECTION}]"
  AUDIO_INJECTION_DIR = '/tmp/injection_audio'
  BROWSERSTACK_AUDIO_HELPER_APP_PACKAGE_NAME = 'com.browserstack.audiohelper'
  BROWSERSTACK_AUDIO_HELPER_APP_PATH = "#{WEBRTC_DIR}/BrowserStackAudioHelper.apk"
  AUDIO_INJECTION_DEVICES = [
    "R3CT40A56BY", "R5CT112C7KP", "R3CT407RKQL", "R5CT11WTQVA", "R5CT11WKP2F",
    "R3CT40ABEPP", "R3CT30WDQZA", "R3CT40BSFWN",
    "R3CRB06LQAB", "R3CR909HYXE", "R3CR909KX2X",
    "26061FDF6005H5", "18191FDF60057K"
  ].freeze
  AUDIO_INJECTION_ERRORS = {
    AL001: {
      message: "Audio Injection Setup Failed"
    },
    AL002: {
      message: "Audio Injection Session Not Running"
    },
    AL003: {
      message: "Failed to Inject Audio"
    },
    AL004: {
      message: "No Audio Injected"
    },
    AL005: {
      message: "No Audio Playing"
    }
  }.freeze

  # Android Sim
  ANDROID_SIM = 'android_sim'
  ANDROID_SIM_TAG = "[#{ANDROID_SIM}]"
  ANDROID_SIM_POOR_SIGNAL_THRESHOLD = -110 # ranges from 0 to -120. Closer to 0 --> stronger signal
  ANDROID_SIM_RULES_CONFIG = "#{PATH}/static/sim_rules_config.json"
  # list of android sim devices on the machine
  ANDROID_SIM_DEVICES_FILE = "/usr/local/.browserstack/mobile/config/custom_devices/android_sim_devices"
  PRIVATE_ANDROID_SIM_DEVICES_FILE = "/usr/local/.browserstack/mobile/config/custom_devices/private_android_sim_devices"
  PRIVATE_CLOUD_CHROME_OVERRIDE_DEVICES_FILE = "#{MOBILE_CUSTOM_DEVICES}/private_cloud_chrome_override_devices.json"

  FILE_DOWNLOAD_ERRORS = {
    max_size_limit_error: 'DOWNLOAD_LIMIT_EXCEEDED',
    files_not_available: 'DOWNLOAD_FILES_NOT_AVAILABLE',
    file_adb_pull_failed: 'ADB_PULL_FAILED'
  }.freeze
  DOWNLOAD_FILES_SUCCESS = 'DOWNLOAD_FILES_SUCCESS'
  DOWNLOAD_FILES_FAILURE = 'DOWNLOAD_FILES_FAILED'

  # Android settings eg : set date time
  ANDROID_SETTINGS_ERRORS = {
    set_time_root_failed: 'SET_DATE_TIME_RUN_AS_ROOT_NOT_WORKING',
    set_time_date_comparison_check_failed: 'SET_DATE_TIME_COMPARISON_CHECK_WITH_EXPECTED_FAILED',
    set_time_validation_error: 'SET_DATE_TIME_VALIDATION_ERROR_NO_DATE_OR_TIME_FOUND'
  }.freeze
  ANDROID_SETTINGS_SET_DATE_TIME = "android_settings_date_time"
  UPDATE_ANDROID_SETTING_SET_DATE_TIME = 'UPDATE_ANDROID_SETTINGS_SET_DATE_TIME'

  PERFORMANCE_STATS_LOGS_DEVICE_PATH = '/data/local/tmp/performance_stats.log'

  BATTERY_ZOMBIE_PUSH_INTERVAL = 5
  OFFLINE_ZOMBIE_PUSH_INTERVAL = rand(10...15).freeze # To push offline data tp BQ only once every
                                                      # 10th to 15th device check

  DOWNLOAD_FILES = "download_files"
  DOWNLOAD_FILES_TAG = "[#{DOWNLOAD_FILES}]"
  DOWNLOAD_FILES_DIR = '/tmp/download_files_android/'
  DOWNLOAD_FILES_DEVICE_DOWNLOAD_PATH = '/storage/emulated/0/Download/'
  DOWNLOAD_FILES_OBB_DOWNLOAD_PATH = '/sdcard/Android/obb/com.dts.freefireth'

  OFFLINE_REASONS = {
    camera_check_app_not_installed: 'Camera check (canisee) app not installed',
    ui_automation_apps_not_installed: 'UI Automation Apps Not Installed',
    no_tun_interface_found: 'no tun interface found, redovpn needed',
    bstack_vpn_not_running: 'internet not working for brt app',
    browserstack_app_not_installed: 'browserstack app is not installed',
    vpn_app_not_installed: 'vpn app is not installed',
    gnirehtet_app_not_installed: 'gnirehtet vpn app is not installed',
    gnirehtet_relay_outdated: 'gnirehtet relay outdated',
    gnirehtet_permission_not_handled: 'gnirehtet needs vpn popup handled',
    bstack_reverse_tether_app_not_installed: 'bstack reverse tether vpn app is not installed',
    bstack_reverse_tether_forwarder_outdated: 'bstack reverse tether forwarder install required',
    play_store_not_installed: 'play_store is not installed',
    unknown_firmware_detected: 'unknown firmware detected',
    device_owner_not_installed: 'device owner not installed',
    device_owner_not_enabled: 'device owner not enabled',
    device_owner_privileges_manual_fix: 'manual fix: needs factory reset to fix device owner privileges',
    system_updates_not_blocked: 'system updates not blocked',
    account_present: 'Account present',
    browserstack_watcher_not_installed: 'browserstack watcher not installed',
    test_orchestrator_app_not_installed: 'test orchestrator app is not installed',
    huawei_browser_app_not_installed: 'huawei browser app is not installed',
    test_services_app_not_installed: 'test services app is not installed',
    appium_apps_not_installed: 'appium apks not found',
    google_maps_not_installed: 'googlemaps app is not installed',
    facebook_app_not_installed: 'facebook app is not installed',
    talkback_app_not_installed: 'talkback app is not installed',
    battery_level_not_found: 'battery level not found',
    run_as_root_reboot_required: "cannot execute run_as_root, rebooting",
    run_as_root_rebooted: "cannot execute run_as_root, rebooted",
    run_as_root_manual_fix_required: "manual fix required: reflash required as cannot execute run_as_root",
    http_proxy_set: "internet down: Connection to http://",
    device_overheated: "device overheated"
  }.freeze
  LOG_DIR_PATH = '/var/log/browserstack'

  WIFI_EXPERIMENT_DEVICES = ["placeholder-device"].freeze
  WIFI_EXPERIMENT_SUB_REGION = ["placeholder-region"].freeze

  # Low battery offline states
  # for state diagram and descriptions: https://browserstack.atlassian.net/browse/MOB-9709?focusedCommentId=291822
  CHARGING_NOW = "low battery: charging now"
  REBOOTED = "low battery: rebooted"
  CHARGING_AFTER_REBOOT = "low battery: charging after reboot"
  REPLUG_REQUIRED_CHARGING_FAILED = "manual fix required: replug required as device not charging"
  BATTERY_LOW_WATERMARK = 25
  BATTERY_HIGH_WATERMARK = 35
  OFFLINE_REASON_TIME_LIMIT = 21600 #time in seconds, equates to 6 hours
  INSTALL_SYSTEM_APP_SCRIPT = "#{PATH}/scripts/install_system_app.sh"
  INSTALL_SYSTEM_APP_WITH_LIBS_SCRIPT = "#{PATH}/scripts/install_system_app_with_libs.sh"
  USB_VPN = "#{BS_DIR}/usbVpn"
  USB_VPN_JAR = "#{BS_DIR}/usbVpn/VpnAuth.jar"
  STATIC_CONF = "#{BS_DIR}/config/static_conf.json"
  BROWSER_PACKAGE_MAP = {
    'chrome' => "com.android.chrome",
    'edge' => "com.microsoft.emmx",
    'firefox' => "org.mozilla.firefox",
    'samsung' => "com.sec.android.app.sbrowser",
    'samsung_browser' => 'com.sec.android.app.sbrowser',
    'internet' => 'com.sec.android.app.sbrowser',
    'ucbrowser' => "com.UCMobile.intl",
    'ucmobile' => 'com.UCMobile.intl',
    'vivo' => 'com.vivo.browser',
    'oppo' => 'com.heytap.browser',
    'opera' => 'com.opera.browser',
    'mi' => 'com.android.browser',
    'huawei' => 'com.huawei.browser'
  }.freeze
  GOOGLE_PHOTOS_PACKAGE = 'com.google.android.apps.photos'
  PERCY = 'percy'
  PERCY_SESSION_TIMEOUT = 300
  ALLOWED_USER_ADB_COMMANDS_CONFIG = "#{MOBILE_CONFIG}/adb_commands.yml"
  WHITELISTED_ADB_COMMANDS = YAML.load_file(ALLOWED_USER_ADB_COMMANDS_CONFIG)
  FEDORA_VERSION = begin
    File.read('/etc/fedora-release').split(" ")[2].strip.to_i
  rescue StandardError
    0
  end

  POOL_MASK = {
    # 32 ( "100000" ) for Audio Injection and 32|7 = 39 ( "100111" ) for moving Audio Injection devices to common pool
    # Pool bits in sequence represent below terminals
    # google_pay_t, esim_t, ios_unrestricted_t, audio_injection_t, apple_pay_t, sim_t, app_t, automate_t, live_t
    audio_injection_device: "0000100111".to_i(2), # audio_injection_device pool = 39
    sim_enabled_device: "0000001111".to_i(2), # sim_enabled_device pool = 15
    google_pay_device: "1000000000".to_i(2) # Google pay terminals pool = 512
  }.freeze

  # Detox Constants
  DETOX_INSTRUMENTATION_LAUNCH_VERIFY_RETRIES = 5
  DETOX_TERMINATE_APP_RETRIES = 3
  DETOX_IGNORED_LAUNCH_ARGS = ["detoxServer", "detoxSessionId"].freeze
  DETOX_CLI = ENV["DETOX_CLI"].freeze || 'detox'
  SIGKILL = -9
  SIGINT = -2

  ANDROID_CRASH_START_PATTERN1 = /\d+\s+\d+ E AndroidRuntime: FATAL EXCEPTION: main/.freeze
  ANDROID_CRASH_BUNDLEID_PATTERN1 = /AndroidRuntime: Process: (.*), PID: (\d+)/.freeze
  ANDROID_CRASH_START_PATTERN2 = /\d+\s+\d+ F libc\s+:\s+Fatal signal \d+ \(SIG\w+\), code \d+(?:\s+\(\w+\))?, fault addr (0x[0-9a-f]+) in tid (\d+) \(([\w.]+)\)/.freeze   # rubocop:todo Layout/LineLength
  ANDROID_CRASH_BUNDLEID_PATTERN2 = /\d+\s+(\d+) F DEBUG\s+:\s+pid:\s+\d+,\s+tid:\s+\d+,\s+name:\s+[\w.-]+\s+>>>\s+([\w.-]+)\s+<<<\s+/.freeze   # rubocop:todo Layout/LineLength

  #qa cleanup suite
  CLEANUP_APP = 'https://qa-live-server.bsstag.com/download/cleanup_app-debug.apk'
  CLEANUP_TEST_APP = 'https://qa-live-server.bsstag.com/download/cleanup_app-debug-androidTest.apk'
  S3_BASE_URL = 'https://s3.us-east-1.amazonaws.com/bs-stag'

  # qa ui automator test
  QA_UI_AUTOMATOR_APP = 'https://qa-live-server.bsstag.com/download/mobileQaApp.apk'
  QA_UI_AUTOMATOR_TEST = 'https://qa-live-server.bsstag.com/download/mobileQaTestSuite.apk'
  QA_TEST_RUNNER = 'com.browserstack.qa.test/androidx.test.runner.AndroidJUnitRunner'
  QA_TEMP_PATH_APP = '/tmp/mobileQaApp.apk'
  QA_TEMP_PATH_TEST_SUITE = '/tmp/mobileQaTestSuite.apk'

  SESSION_RELATED_FILES = [
    "#{STATE_FILES_DIR}/cleanupdone_device_id",
    "#{STATE_FILES_DIR}/session_device_id",
    "#{STATE_FILES_DIR}/manual_cleanupdone_device_id",
    "#{STATE_FILES_DIR}/extra_access_device_id",
    "#{STATE_FILES_DIR}/click_power_off_button_device_id",
    "/tmp/duplicate_session_device_id",
    "/tmp/*app_session_device_id",
    "/tmp/sessionis_device_id",
    "/tmp/sessionbrowseris_device_id",
    "/tmp/unclean_low_battery_device_id",
    "/tmp/sdcard_new_device_id",
    "/tmp/browserstack_system_app_device_id",
    "/tmp/snapshot_device_id",
    "/tmp/cleanup_reason_device_id",
    "#{STATE_FILES_DIR}/cleanup_failure_reason_device_id",
    "/tmp/network_simulation_device_id",
    "/tmp/internet_via_usb_device_id",
    "/tmp/mitm_flow_file_device_id.txt",
    "/tmp/device_id-instrumentation.log",
    "/tmp/video_offset_touch_files/session_id",
    "/tmp/ALL_APPS_device_id",
    "#{STATE_FILES_DIR}/needs_reboot_device_id",
    "/tmp/allow_limited_network_device_id",
    "#{STATE_FILES_DIR}/adb_command_execution_device_id",
    "#{STATE_FILES_DIR}/device_logger_unreliable_device_id",
    "/tmp/android_launched_apps/session_id",
    "#{STATE_FILES_DIR}/samsung_browser_used_device_id",
    OPENED_BROWSERS_FILE
  ].freeze

  ROOT_ACCESS_SESSION_RELATED_FILES = [
    "/tmp/device_id_track_process.txt",
    "/tmp/device_id_factory_reset.txt",
    "#{STATE_FILES_DIR}/needs_manual_factory_reset_device_id",
    "/tmp/device_id_app_crash.txt",
    "#{STATE_FILES_DIR}/unclean_off_adb_device_id",
    "/tmp/frozen_device_id",
    "/tmp/device_id_outside_browser",
    "/tmp/device_id_cleanup_count",
    "/tmp/device_id_is_rooted",
    "/tmp/PLAYSTORE_APPS_device_id",
    "/tmp/UPLOADED_APPS_device_id",
    "/tmp/ALL_SYSTEM_AND_UPLOADED_APPS_device_id",
    "/tmp/check_statusbar_log_device_id"
  ].freeze

  MEDIA_PROJECTION_COORDINATES_JSON = "#{PATH}/static/media_projection_coordinates.json"
  FLUTTER_ANDROID_FALLBACK_INSTRUMENTATION_LOGS = "#{PATH}/static/flutter_android_fallback_instrumentation_logs.log"

  PLAYSTORE_APPS = "/tmp/PLAYSTORE_APPS_device_id"
  UPLOADED_APPS = "/tmp/UPLOADED_APPS_device_id"
  ALL_SYSTEM_AND_UPLOADED_APPS = "/tmp/ALL_SYSTEM_AND_UPLOADED_APPS_device_id"
  DUPLICATE_SESSION_FILE = "/tmp/duplicate_session_device_id"

  COINBLOCKERLISTS_URL = "https://gitlab.com/ZeroDot1/CoinBlockerLists/-/raw/master/list.txt"
  DNS_NETWORK_LOGS_PATH = "/sdcard/Android/data/com.browserstack.deviceowner/files/."

  AI_PROXY_PORT_OFFSET = 5555 #update this to 5555
  ANDROID = "android"

  PLAYWRIGHT_ANDROID_PORT_OFFSET = 1200
  CDP_PORT_OFFSET = 1000
  SOCAT_PORT_OFFSET = 1400
  BS_IME = "fr.pchab.AndroidRTC2/.BrowserStackIME"
  SAMSUNG_BATTERY_FILE = "/sys/class/power_supply/battery/batt_slate_mode"
  DEVICE_LOGGER_SESSION_START_DIR = "#{STATE_FILES_DIR}/session_start"
  DL_EVENT_CAPTURE_FILE_PREFIX = "#{STATE_FILES_DIR}/dl_instrumentations/chowkidar_instrumentation_"
  CLEANUP_FAILURE_REASON_FILE = "#{STATE_FILES_DIR}/cleanup_failure_reason_device_id"
  CLEANUP_LOG_FILE = "/var/log/browserstack/cleanup_device_id.log"
  CLEANUP_REASON_TMP = "/tmp/cleanup_reason_device_id"
  SESSION_FILE = "#{STATE_FILES_DIR}/session_device_id"
  APP_INTERNET_VIA_USB_FILE = "/tmp/app_internet_via_usb_device_id"
  NETWORK_SIMULATION_FILE = "/tmp/network_simulation_device_id"
  LIMIT_NETWORK_FILE = "/tmp/allow_limited_network_device_id"
  TEMP_APP_CRASH_FILE = "/data/local/tmp/app_crash.txt"
  APP_CRASH_FILE = "/tmp/device_id_app_crash.txt"
  VPN_APP_CRASH_FILE = "#{STATE_FILES_DIR}/vpn_app_crash_device_id"
  FORWARDER_CRASHED_FILE = "#{STATE_FILES_DIR}/forwarder_crashed_device_id"
  BRT_STARTED_FOR_BSTACK_APPS_FILE = "#{STATE_FILES_DIR}/brt_started_for_bstack_apps"
  RECOVER_DEVICE_FILE = "#{STATE_FILES_DIR}/recover_device_command_"
  BROWSERSTACK_APPS = [
    BROWSERSTACK_APP_PACKAGE_NAME,
    CAMERA_CHECK_PACKAGE_NAME,
    INPUT_INJECTOR_PACKAGE_NAME,
    BROWSERSTACK_WATCHER_PACKAGE_NAME,
    BROWSERSTACK_DEVICE_OWNER_PACKAGE_NAME,
    UI_AUTOMATION_APP_PACKAGE_NAME,
    RTCAPP_PACKAGE_NAME,
    RTCAPP_2_PACKAGE_NAME
  ].freeze
  CUSTOM_APPS = {
    'geoguard' => "com.geocomply.oobeeapp"
  }.freeze

  BOOTSNAP_SUPPORTED_FILES = ["main_cleanup.rb", "ui_automation_apps_manager.rb", "android_device.rb", "pre_cleanup.rb", "popup_helper.rb", "browserstack_app_manager.rb", "disable_apps.rb", "replugging_script.rb", "internet_healthcheck.rb", "chrome_release_installer.rb", "samsung_browser_manager.rb", "bstack_reverse_tether_controller.rb", "logcat_buffer_manager.rb", "usb_vpn.rb", "network_usage_tracker.rb", "screen_lock_helper.rb", "firefox_manager.rb", "uc_browser_manager.rb", "edge_manager.rb", "set_date_time_helper.rb", "camera_check_manager.rb", "restriction_scripts_manager.rb", "contacts_helper.rb", "app_injection.rb", "audio_injector.rb", "device_sim_helper.rb", "google_pay_helper.rb", "media_manager.rb", "unknown_apps_check.rb", "battery_update.rb", "upgrade_popup_helper.rb", "grant_permission.rb", "check_camera.rb", "chrome_flags_helper.rb", "browser_helper.rb", "live_media_injector.rb", "account_helper.rb", "emergency_alerts.rb", "system_update_block_helper.rb", "pdfviewer_app_manager.rb", "post_cleanup.rb"].freeze  # rubocop:todo Layout/LineLength
  BOOTSNAP_SUPPORTED_REGION = ["us-east-1d", "ap-southeast-2c", "eu-west-1d", "us-east-1c", "eu-west-1b", "us-west-1e", "eu-west-1c", "us-west-1d", "ap-south-1c", "ap-south-1d", "eu-central-1a"].freeze  # rubocop:todo Layout/LineLength

  S3_STAGING_BUCKET = "bs-stag"
  S3_PLATFORM_BUCKET = "bs-platform"
  LOCALHOST_IP = "127.0.0.1"
  BROADCASTHOST_IP = "***************"
  BLOCKED_DOMAIN_PAGE_IP = "**************"

  GROUP_DOMAIN_BLOCKING_FLAG = "#{STATE_FILES_DIR}/enable_group_domain_blocking"
  CLEANED_STATE_FILE = "#{STATE_FILES_DIR}/domain_block_whitelist_cleaned"
  DOMAINS_CONFIG_JSON_FILE = "#{STATE_FILES_DIR}/group_based_domain_status.json"
  BLOCK_DOMAINS_FILE = "#{STATE_FILES_DIR}/privoxy_blocked_domains.txt"
  WHITELIST_DOMAINS_FILE = "#{STATE_FILES_DIR}/privoxy_whitelisted_domains.txt"
  ETC_HOST_DOMAINS = "#{STATE_FILES_DIR}/etc_host_domains.txt"
  DOMAIN_STATUS_JSON_FILE = "#{STATE_FILES_DIR}/domain_status.json"

  MAESTRO_CONFIG = {
    default_version: "1.39.13"
  }.freeze

  TEST_STATUS = {
    PASSED: "passed",
    FAILED: "failed",
    SKIPPED: "skipped",
    RUNNING: "running",
    TIMEOUT: "timeout",
    QUEUED: "queued",
    ERROR: "error"
  }.freeze

  FRAMEWORKS = {
    ESPRESSO: "espresso",
    FLUTTER: "fluttertest"
  }.freeze

  LOG_SPLIT_FEATURE_USAGE = {
    videoLogSplitting: "videoLogSplitting",
    networkLogSplitting: "networkLogSplitting",
    deviceLogSplitting: "deviceLogSplitting"
  }.freeze

  LOG_SPLIT_ERRORS = {
    device_log_split_error: "device_log_split_error",
    video_log_split_error: "video_log_split_error",
    network_log_split_error: "network_log_split_error",

    network_log_split_failure: "network_log_split_failures",
    video_log_split_failure: "video_log_split_failures",
    device_log_split_failure: "device_log_split_failures",

    network_log_split_fallback: "network_log_split_fallback"
  }.freeze
  ENV_START_REQUEST_MIDDLEWARE_PATHS = [
    "/start",
    "/app_start",
    "/selenium_command",
    "/start_espresso_session",
    "/start_fluttertest_session",
    "/start_maestro_session",
    "/percy/start_server"
  ].freeze
  ENV_NON_START_REQUEST_MIDDLEWARE_PATHS = [
    "/stop",
    "/cleanup",
    "/percy/stop_server",
    "/percy/minified_cleanup"
  ].freeze

  MOBILE_DATA_ELIGIBLE_PRODUCTS = [
    'live_testing',
    'app_live_testing',
    'app_automate',
    'selenium',
    'automate',
    'js_testing'
  ].freeze

  SECRET_SCANNING_WHITELISTED_REQUESTS = {
    "/snapshot_hub" => {
      "query_hash" => [
        "key"
      ]
    }
  }.freeze
end
