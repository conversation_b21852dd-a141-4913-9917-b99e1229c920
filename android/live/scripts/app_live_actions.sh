#!/usr/bin/env bash
set -x

# Default install timeout as per appium
INSTALL_TIMEOUT=400 #this is max install time for an app we have seen. Only gets honoured for experiment.

ACTION="$1"
DEVICEID="$2"
INTERACTION_ARGS="$3"
WIDTH="$4"
HEIGHT=0
ASYNC="$5"
TUN_IP="$6"
PROXY_PORT="$7"
APP_DOWNLOAD_URL="$8"
APP_DOWNLOAD_TIMEOUT="${20}"
INSTRUMENTATION_TYPES="${21}"
INSTRUMENTATION_PARAMS="${22}"
LOCAL_UPDATE="${9}"
APP_HASHED_ID="${10}"
launch_activity="${11}"
package="${12}"
relaunch_flow="${13}"
play_store_disabled="${14}"
install_experiment_enabled="${15}"
synchronous_install="${16}"
should_zip_align="${17}"
AUDIO="${23}"
IS_BIOMETRIC_TOGGLED="${24}"
IS_CAMERA_TOGGLED="${25}"
USE_RTC_APP="${26}"
IS_WEBVIEW_TOGGLED="${27}"
IS_SCREENSHOT_BLOCK_TOGGLED="${28}"
IS_NETWORK_LOGS_ENABLED="${29}"
LOCALE="${30}"
IS_PASSCODE_TOGGLED="${31}"
USE_RTC_APP_AUDIO="${32}"
ENABLE_CONTACTS_APP_ACCESS="${33}"
ENABLE_AUDIO_INJECTION="${34}"
ENABLE_ROTATION_USING_APP_ORIENTATION="${35}"
OBB_FILE_URL="${36}"
OBB_FILE_NAME="${37}"
ENABLE_BS_IME="${38}"
ENABLE_SIM="${39}"
IS_SYNC_WEBRTC="${40}"
USERNAME="${41}"
IS_NETWORK_LOGS_PATCH_TOGGLED="${42}"
IS_CAMERA_PREVIEW_LAYER_TOGGLED="${43}"
IS_VIDEO_TOGGLED="${44}"
IS_APP_ACCESSIBILITY="${45}"
USE_INSTANT_APP="${46}"
HANDLE_MP_DURING_ROTATION="${47}"
ASYNC_APP_LAUNCH_SETUP_ENABLED="${48}"
APP_HAS_TRUSTED_USER_CERT="${49}"
ASYNC_METADATA_EXTRACTION_ENABLED="${50}"
DEV_TOOL_DEBUGGER_PORT="${51}"
SKIP_APK_SIGNING_FLOW="${52}"
LOG_EVENTS="true"
switch_app="false"
APP_PATH="/tmp/app-${DEVICEID}.apk"
USERS_PATCHED_APP_PATH="/tmp/app-${DEVICEID}-patched.apk"
PATCHING_TRACKING_FILE="/tmp/patching_app_$DEVICEID"
ZIPALIGNED_APP_PATH="/tmp/app-${DEVICEID}-zipaligned.apk"
DUP_APP_PROFILE_LOG="/tmp/app_profiling_log_${DEVICEID}.csv"
STATE_FILES_DIR="/usr/local/.browserstack/state_files"
SESSION_START_DIR="$STATE_FILES_DIR/session_start"
MOBILE_RTC_CONFIG="/sdcard/rtc_service"
MOBILE_PACKAGE_FILE="/sdcard/app_package_name"
ANDROID_HOME_PATH="/usr/local/.browserstack/android-sdk"
BUILD_TOOLS_PATH="${ANDROID_HOME_PATH}/build-tools/29.0.2"
EXTRAS_PATH="${ANDROID_HOME_PATH}/extras"
INSTANT_APP_IA_PATH="${EXTRAS_PATH}/google/instantapps/ia"
BUNDLETOOL_PATH="/usr/local/.browserstack/deps/bundletool/bundletool.jar"
ZIPALIGN_PATH="${BUILD_TOOLS_PATH}/zipalign"
ASYNC_METADATA_EXTRACTION_PATH="/usr/local/.browserstack/mobile/android/helpers/metadata_extraction.rb"
OBB_FILE_HELPER_LOG = "[obb_file_helper_log]"
#pushing this file in device to indicate it's APP_LIVE_SESSION
#live_common
GENRE="app_live_testing"
MAX_INSTALL_RETRIES=5
ZIP_ALIGN_STATUS="false"          # to check if app was zip_aligned while signing.
ASYNC_APP_LAUNCH_SYNC_PIPE_FILE="/tmp/async_app_launch_setup_$DEVICEID.fifo"    # Named pipe file to synchronize app download, app install, streaming setup and app launch
ASYNC_APP_LAUNCH_PIPE_TIMEOUT=60     # Timeout for blocking commands which accesses the named pipe (FIFO) file

source /usr/local/.browserstack/mobile/android/live_common.sh
source /usr/local/.browserstack/mobile/android/helpers/version_comparison.sh
source /usr/local/.browserstack/mobile/android/live/scripts/android_performance.sh
source /usr/local/.browserstack/mobile/android/live/scripts/patch_user_apk.sh

APP_LIVE_TESTING_FILE="$SESSION_DIR/bs-session-app-live.txt"
#$1 = adb_options = "-r"
is_app_unsigned_by_apksigner_status=-1
is_app_unsigned_status=-1
platform=`uname`
if [ $platform == "Linux" ]; then
  timeout="timeout"
else
  timeout="gtimeout"
fi

if [ "$IS_APP_ACCESSIBILITY" == "true" ]; then
  TEAM_PRODUCT_STR=",team:app_a11y_dev,product:app_accessibility"
  EVENT_TYPE="web_events"
else
  TEAM_PRODUCT_STR=""
  EVENT_TYPE="app_live_web_events"
fi

forward_debugger_port(){
	adb -s $DEVICEID forward tcp:$DEV_TOOL_DEBUGGER_PORT $DEBUG_PORT
}

notify_device_logger() {
  # Used for start and update events to device-logger
  start_device_logger &
}

increase_media_sound() {
  get_device_model
  if device_is_a disable_sound; then
    echo "increasing media sound"
    adb -s $DEVICEID shell "cmd media_session volume --stream 3 --set 15"
  fi
}

install_app_helper() {
  notify_event "app_install_start" &
  if [[ "$switch_app" == "false" ]]; then
    # Start device logger to setup streamLogs watcher before frontend sends stream_logs event
    notify_device_logger
  fi
  # Do not install, if it is instant app
  if [ "$USE_INSTANT_APP" != "true" ]; then
    install_app "$package" "$1" 0 #TODO restore
  fi

  notify_event "app_install_end" &
}

launch_app() {

  notify_event "app_launch_start" &
  if [[ "$switch_app" == "false" ]]; then
    # Update session for device logger
    notify_device_logger
  fi
  if [[ -z "$launch_activity" ]]; then
    notify_event "app_manual_start"
  fi
  session_id=$(cat /tmp/app_live_logs_params_$DEVICEID | cut -d " " -f2)
  start=$(($(date +%s%N)/1000000))
  launch_output_file="/tmp/app_live_${DEVICEID}_${session_id}_launchapp"
  adb -s "$DEVICEID" shell input keyevent 3
  get_os_version
  if vers_gte "$OS_VERSION" "14"; then
    adb -s $DEVICEID am force-stop com.android.vending
  fi
  if [ "$USE_INSTANT_APP" == "true" ]; then
    $INSTANT_APP_IA_PATH run -s "$DEVICEID" "$APP_PATH" 2>$launch_output_file
  else
    if [[ ! -z "$launch_activity" ]]; then
      if vers_gte "$OS_VERSION" "13" && [[ "$package" == "com.android.vending" ]]; then
        adb -s "$DEVICEID" shell am start -n "${package}/${launch_activity}" 2>$launch_output_file
      else
        adb -s "$DEVICEID" shell am start -S "${package}/${launch_activity}" 2>$launch_output_file
      fi
    else
      echo "Launch activity is empty"
    fi
  fi
  grep "Error" "${launch_output_file}"
  if [ $? == 0 ]; then
    echo "App Launch Failed"
    adb_launch_out="false"
    error=""
    if [ -s "$launch_output_file" ]; then
      error=$(cat $launch_output_file)
    fi
  else
    adb_launch_out="true"
    if [[ "$APP_HASHED_ID" == "booking" ]]; then
      sleep 2
      adb -s "$DEVICEID" shell input keyevent 3
      adb -s "$DEVICEID" shell am start -S "${package}/${launch_activity}"
    fi
  fi
  rm $launch_output_file
  end=$(($(date +%s%N)/1000000))
  timetaken=$((end-start))
  $BS_DIR/mobile-common/common/eds.rb "/tmp/duplicate_session_$DEVICEID" "success:${adb_launch_out},launch_time:${timetaken},event_name:AndroidLaunch,session_id:${session_id},error:${error}$TEAM_PRODUCT_STR" "$EVENT_TYPE" "false"

  if is_device_pointed_to_prod; then
    $BS_DIR/mobile/common/push_to_zombie.rb "android" "app-live-apk-launch-status" "apk-launch-success:${adb_launch_out}, app_hashed_id:${APP_HASHED_ID}, package:${package}/${launch_activity}, error:${error}" "android" "${timetaken}" "$DEVICEID" "${session_id}" "$USER_ID" "$DEVICE_VERSION"
  fi
  notify_event "app_launch_end" &
}

sign_and_zipalign() {
  is_app_unsigned_status=$(is_app_unsigned ${download_path})
  if [[ $is_app_unsigned_status -eq 1 ]] && [[ $(verify_zip_aligned_app ${download_path}) -eq 1 ]]
  then
    sign_app
  else
    if [[ $1 = 'true' ]]; then
      zip -d "${download_path}" "META-INF/*.RSA" "META-INF/*.SF" "META-INF/*.MF";
      is_app_unsigned_status=0
    fi
    sign_app
    if [[ $1 = 'true' ]]; then zip_align_app "${download_path}" "$ZIPALIGNED_APP_PATH" "$SESSION_ID"; fi
    ZIP_ALIGN_STATUS="true"
  fi
}

sign_with_apksigner_and_zipalign() {
  # zipalign if not already signed and zipaligned
  is_app_unsigned_by_apksigner_status=$(is_app_unsigned_by_apksigner "${download_path}")
  if [[ $is_app_unsigned_by_apksigner_status -eq 0 ]] && $(verify_zip_aligned_app "${download_path}"); then
    zip_align_app "${download_path}" "$ZIPALIGNED_APP_PATH" "$SESSION_ID";
    ZIP_ALIGN_STATUS="true"
  fi
  sign_app
}

sign_and_install_app() {
  if [[ $SKIP_APK_SIGNING_FLOW == "true" ]]; then
    echo "Skipping APK signing flow"
  else
    notify_event "app_signing_start" &
    SESSION_ID=$(cat /tmp/app_live_logs_params_$DEVICEID | cut -d " " -f2)
    download_path=$APP_PATH
    if [[ ${enable_apksigner} == 'true' ]]; then
      sign_with_apksigner_and_zipalign
    else
      sign_and_zipalign "$should_zip_align"
    fi
    notify_event "app_signing_end" &
  fi

  install_app_helper
}

start_app() {
  sign_and_install_app
  launch_app
}

is_device_pointed_to_prod() {
  cat << RUBY | /home/<USER>/bin/bundle exec ruby
  require 'static_conf';
  c=StaticConf::StaticConfHelper.setup;
  if c.device_pointed_to_prod?('$DEVICEID')
    exit 0
  else
    exit 1
  end
RUBY
}

download_app() {
  get_device_model
  get_os_version
  if (vers_gte "$OS_VERSION" "13" && [[ "$ASYNC_APP_LAUNCH_SETUP_ENABLED" != "true" ]] && [[ "$device_model" == "Pixel 6 Pro" ]]) || [[ "$device_model" == "Pixel 7" ]] || [[ "$device_model" == "Pixel 7 Pro" ]] || [[ "$device_model" == "Pixel 6a" ]]; then
    # MOBFR-279: race condition between starting app and starting media projection streaming
    sleep 5
  fi
  SESSION_ID=$(cat /tmp/app_live_logs_params_$DEVICEID | cut -d " " -f2)
  notify_device_logger


  if [ "$OBB_FILE_URL" != "" ]; then
    notify_event "OBB download started,${SESSION_ID}"
    $BUNDLE exec ruby "$BS_DIR/mobile/android/helpers/obb_file_helper.rb" "download" "$DEVICEID" "$SESSION_ID" "$package" "$OBB_FILE_URL" "$OBB_FILE_NAME"
    if [ -f "$STATE_FILES_DIR/obb_file_downloaded_$DEVICEID" ]; then
      echo "$OBB_FILE_HELPER_LOG [$SESSION_ID] Obb file successfully downloaded"
      notify_event "OBB download done and pushing obb started,${SESSION_ID}"
      rm "$STATE_FILES_DIR/obb_file_downloaded_$DEVICEID"
      $BUNDLE exec ruby "$BS_DIR/mobile/android/helpers/obb_file_helper.rb" "push" "$DEVICEID" "$SESSION_ID" "$package" "$OBB_FILE_URL" "$OBB_FILE_NAME"
      if [ -f "$STATE_FILES_DIR/obb_file_injected_$DEVICEID" ]; then
        echo "$OBB_FILE_HELPER_LOG [$SESSION_ID] File got downloaded and pushed."
        notify_event "Pushing OBB to device done,${SESSION_ID}"
        rm "$STATE_FILES_DIR/obb_file_injected_$DEVICEID"
      elif [ -f "$STATE_FILES_DIR/obb_file_failure_$DEVICEID" ]; then
        obb_error=$(cat "$STATE_FILES_DIR/obb_file_failure_$DEVICEID")
        echo "$OBB_FILE_HELPER_LOG [$SESSION_ID] Some error occured.$obb_error"
        notify_event "Pushing OBB to device failed,${SESSION_ID}"
        rm "$STATE_FILES_DIR/obb_file_failure_$DEVICEID"
        return 1
      fi
    else
      echo "$OBB_FILE_HELPER_LOG [$SESSION_ID] Obb file download failed"
      notify_event "OBB download failed,${SESSION_ID}"
      return 1
    fi
  fi

  notify_event "app_download_start" &
  if [ "$INSTRUMENTATION_TYPES" != "[]" ]; then
    if [ "$IS_BIOMETRIC_TOGGLED" == "true" ]; then
      notify_event "Biometric_Auth_Started,${SESSION_ID}"
    fi
    if [ "$IS_CAMERA_TOGGLED" == "true" ]; then
      notify_event "Image_Injection_Started,${SESSION_ID}"
    fi
    if [ "$IS_CAMERA_PREVIEW_LAYER_TOGGLED" == "true" ]; then
      notify_event "Preview_Layer_Started,${SESSION_ID}"
    fi
    if [ "$IS_VIDEO_TOGGLED" == "true" ]; then
      notify_event "Video_Injection_Started,${SESSION_ID}"
    fi
    if [ "$IS_WEBVIEW_TOGGLED" == "true" ]; then
      notify_event "Webview_Injection_Started,${SESSION_ID}"
    fi
    if [ "$IS_SCREENSHOT_BLOCK_TOGGLED" == "true" ]; then
      notify_event "Screenshot_Flow_Started,${SESSION_ID}"
    fi
    if [ "$INSTRUMENTATION_PARAMS" != "{}" ]; then
      $BUNDLE exec ruby "$BS_DIR/mobile/android/lib/app_actions.rb" "$DEVICEID" "$INSTRUMENTATION_PARAMS"
      APP_DOWNLOAD_URL=$(cat /tmp/app_live_download_url_$DEVICEID)
      rm /tmp/app_live_download_url_$DEVICEID
    fi
    if [ ! -z "$APP_DOWNLOAD_URL" ]; then
      if [ "$IS_BIOMETRIC_TOGGLED" == "true" ]; then
      notify_event "Biometric_Auth_Success,${SESSION_ID}" &
      fi
      if [ "$IS_CAMERA_TOGGLED" == "true" ]; then
        notify_event "Image_Injection_Success,${SESSION_ID}" &
      fi
      if [ "$IS_CAMERA_PREVIEW_LAYER_TOGGLED" == "true" ]; then
        notify_event "Preview_Layer_Success,${SESSION_ID}"
      fi
      if [ "$IS_VIDEO_TOGGLED" == "true" ]; then
        notify_event "Video_Injection_Success,${SESSION_ID}"
      fi
      if [ "$IS_WEBVIEW_TOGGLED" == "true" ]; then
        notify_event "Webview_Injection_Success,${SESSION_ID}" &
      fi
      if [ "$IS_SCREENSHOT_BLOCK_TOGGLED" == "true" ]; then
        notify_event "Screenshot_Flow_Success,${SESSION_ID}" &
      fi
    else
      if [ "$IS_BIOMETRIC_TOGGLED" == "true" ]; then
        notify_event "Biometric_Auth_Failed,${SESSION_ID}" &
      fi
      if [ "$IS_CAMERA_TOGGLED" == "true" ]; then
        notify_event "Image_Injection_Failed,${SESSION_ID}" &
      fi
      if [ "$IS_CAMERA_PREVIEW_LAYER_TOGGLED" == "true" ]; then
        notify_event "Preview_Layer_Failed,${SESSION_ID}"
      fi
      if [ "$IS_VIDEO_TOGGLED" == "true" ]; then
        notify_event "Video_Injection_Failed,${SESSION_ID}"
      fi
      if [ "$IS_WEBVIEW_TOGGLED" == "true" ]; then
      notify_event "Webview_Injection_Failed,${SESSION_ID}" &
      fi
      if [ "$IS_SCREENSHOT_BLOCK_TOGGLED" == "true" ]; then
        notify_event "Screenshot_Flow_Failed,${SESSION_ID}" &
      fi
    fi
  fi
  error=""
  if [ ! -z "$APP_DOWNLOAD_URL" ]; then
    start=$(($(date +%s%N)/1000000))
    status_code=$(curl --max-time $APP_DOWNLOAD_TIMEOUT --retry 3 -o "$APP_PATH" -w "%{http_code}" "$APP_DOWNLOAD_URL")
    exit_code=$?
    if [[ "$status_code" -eq 200 && "$exit_code" -eq 0 ]]; then
      notify_event "app_download_end" &
      end=$(($(date +%s%N)/1000000))
      timetaken=$((end-start))
      $BS_DIR/mobile-common/common/eds.rb "/tmp/duplicate_session_$DEVICEID" "download_time:${timetaken},event_name:AndroidDownload,status:success,instrumentation_type:${INSTRUMENTATION_TYPES}" "app_live_web_events" "false"
      if [ "$IS_NETWORK_LOGS_PATCH_TOGGLED" == "true" ]; then
        echo "[$SESSION_ID] App patched with network logs patch not patching with apk-mitm"
        rm -f "$USERS_PATCHED_APP_PATH"
        notify_event "app_network_logs_patch_successful" &
      else
        check_and_patch_app &
      fi
      if [[ "$ASYNC_METADATA_EXTRACTION_ENABLED" == "true" ]]; then
        metadata_file="/tmp/app_live_async_metadata_response_$DEVICEID"

        $BUNDLE exec ruby "$ASYNC_METADATA_EXTRACTION_PATH" "$APP_PATH" "$DEVICEID" "$SESSION_ID"

        success=$(jq -r '.success' "$metadata_file")

        if [[ "$success" != "true" ]]; then
          rm $metadata_file
          exit 0
        fi

        package=$(jq -r '.app_data.package' "$metadata_file")
        launch_activity=$(jq -r '.app_data.launcher_activity' "$metadata_file")

        rm $metadata_file
      fi
      return 0
    fi
  else
    echo "App download failed because download_url is empty for: ${INSTRUMENTATION_TYPES}"
    error="download_url empty"
  fi

  echo "App download failed with status_code: "$status_code", exit_code: "$exit_code""
  notify_event "app_download_failed" &
  end=$(($(date +%s%N)/1000000))
  timetaken=$((end-start))
  $BS_DIR/mobile-common/common/eds.rb "/tmp/duplicate_session_$DEVICEID" "download_time:${timetaken},event_name:AndroidDownload,status:failed,instrumentation_types:${INSTRUMENTATION_TYPES}, error:${error}$TEAM_PRODUCT_STR" "$EVENT_TYPE" "false"
  if is_device_pointed_to_prod && [[ "$IS_APP_ACCESSIBILITY" != "true" ]]; then
    $BS_DIR/mobile/common/push_to_zombie.rb "android" "app-live-apk-download-failed" "apk-download-status:${status_code}, curl-exit-code:${exit_code}, instrumentation_types:${INSTRUMENTATION_TYPES}, error:${error}" "android" "${timetaken}" "$DEVICEID" "$SESSION_ID"
    $BUNDLE exec ruby "$BS_DIR/mobile/common/hooter.rb" "send_uptime_metric" "$USERNAME" "app-live" "apk_install_failure"
  fi
  return 1
  #disable temporarily
#  file_type=$(file $APP_PATH)
#
#  if [[ $(echo "$file_type" | grep -ic "${APP_PATH}: Zip") -eq 0 && $(echo "$file_type" | grep -icE "${APP_PATH}:.*\(JAR\)") -eq 0 ]]; then
#      notify_pusher "invalid_app_file" &
#      exit
#  fi
}

set_app_info() {
  echo "$package" >> "/tmp/app_session_${DEVICEID}"
  echo "$package" > "/tmp/current_app_session_${DEVICEID}"
  adb -s $DEVICEID push "/tmp/current_app_session_${DEVICEID}" "$MOBILE_PACKAGE_FILE"
}

is_package_installed_from_playstore() {
  if vers_gte "$OS_VERSION" "7"; then
    all_apps=`adb -s $DEVICEID shell cmd package list packages -u -i -3`
  else
    all_apps=`adb -s $DEVICEID shell pm list packages -u -i -3`
  fi
  playstore_apps_end=`echo "$all_apps" | grep -E 'installer=com.android.vending' | awk -F":| " '{print $2}'`
  if [[ ! -z "${playstore_apps_end// }" ]] && [ -f "/tmp/PLAYSTORE_APPS_${DEVICEID}" ]; then
    playstore_apps=`diff <(echo "$playstore_apps_end") "/tmp/PLAYSTORE_APPS_${DEVICEID}" | grep "^<" | cut -c3-`
    if [[ $(echo "$playstore_apps" | grep -Fxc "$1") -eq 1 ]]; then
      echo "yes"
    else
      echo "no"
    fi
  else
    echo "no"
  fi
}

reinstall_and_notify() {
  remove_app "$1"
  notify_pusher "App is already present. Uninstalling it" &
  cls_log_message "Uninstalled ${package} since it is already present" &
  install_app "$1" "$2" $3 "$4" "$5" "$6"
}

install_app() {
  package="$1"
  adb_options=$2
  attempts="$3"
  install_with_consent=${4:-"false"}
  start=${5:-$(($(date +%s%N)/1000000))}
  download_path=${6:-$APP_PATH} # default
  get_device_model
  if [[ "$IS_NETWORK_LOGS_ENABLED" == "true" ]] && ! device_is_a bsrun_device; then
    # wait while user app is patching
    while [ -f "$PATCHING_TRACKING_FILE" ]; do
      echo "App is patching"
      sleep 1s
    done
    if [ -f "$USERS_PATCHED_APP_PATH" ]; then
      echo "Using Mitmcert patched apk: ${USERS_PATCHED_APP_PATH}"
      download_path=${6:-$USERS_PATCHED_APP_PATH}
    fi
  fi
  cpu_before_install=$(cpuUsageForDevice $DEVICEID)
  rtc_cpu_consumption=$(cpuUsageForApp $DEVICEID "fr.pchab.AndroidRTC")

  if [[ "$attempts" -ge 10 ]]; then
    cls_log_message "app_install_error: Install failure after all retries - ${package}" &
    exit 1
  fi

  # somewhere in entire cleanup flow nexus 5 comes out with package_verifier_user_consent as 1
  # hence explicitly setting to -1 for now avoid installation failures AA-6620
  if [[ "$device_model" = "Nexus 5" ]]; then
    set_package_verifier_user_consent "-1"
  fi

  if [ "$install_with_consent" == "true" ]; then
    adb -s $DEVICEID install $adb_options "${download_path}" 2>&1 &
    start_ui_automation=$(($(date +%s%N)/1000000))
    run_ui_automation_check_package_installed $package 60
    retVal=$?
    end_ui_automation=$(($(date +%s%N)/1000000))
    timetaken_ui_automation=$((end_ui_automation-start_ui_automation))
    if [ $retVal -ne 0 ]; then
      echo "App installation with uiautomation failed, falling back to old method"
      adb_install_out="Failed"
    else
      adb_install_out="Success"
    fi
  else
    if [[ "$install_experiment_enabled" == "true" ]]; then
      start_adb_push=$(($(date +%s%N)/1000000))
      adb -s $DEVICEID push "${download_path}" "/data/local/tmp/app-${DEVICEID}.apk"
      end_adb_push=$(($(date +%s%N)/1000000))
      timetaken_adb_push=$((end_adb_push-start_adb_push))
      adb_install_out=$(timeout --signal=SIGTERM $INSTALL_TIMEOUT adb -s ${DEVICEID} shell pm install $adb_options "/data/local/tmp/app-${DEVICEID}.apk" 2>&1)
      retVal=$?
      if [ $retVal -eq 124 ]; then
        adb_install_out="TIMEDOUT"
      fi
    else
      # there is no timeout here ? We do not wait for this to finish (fircmd is timedout and retried in some cases we are seeing smd getting triggerd)
      adb_install_out=$(adb -s $DEVICEID install $adb_options "${download_path}" 2>&1)

      if [[ $SKIP_APK_SIGNING_FLOW == "true"  ]] && [[ $adb_install_out == *INSTALL_PARSE_FAILED_NO_CERTIFICATES* ]]; then
        echo "Signing and zipaligning the app - fallback"
        SKIP_APK_SIGNING_FLOW="false"
        sign_and_install_app
        return
      fi

      start_ui_automation=$(($(date +%s%N)/1000000))
      run_ui_automation_check_package_installed $package 2
      end_ui_automation=$(($(date +%s%N)/1000000))
      timetaken_ui_automation=$((end_ui_automation-start_ui_automation))
    fi
  fi
  end=$(($(date +%s%N)/1000000))
  timetaken=$((end-start))

  cpu_after_install=$(cpuUsageForDevice $DEVICEID)
  case "$adb_install_out" in
    *Success*)
      echo "App installed successfully with adb"
      if [[ $(adb -s $DEVICEID shell pm list packages | grep -c "${package}" ) -eq 0 ]]; then
        # In case when adb succeeds but it's not added in the device packages
        cls_log_message "app_install_info: Install success, but package not found in device packages, retrying" &
        install_app "$package" "$adb_options" $((attempts + 1)) "false" "${start}" "$download_path"
      else
        echo "App installed on device"
        $BS_DIR/mobile-common/common/eds.rb "/tmp/duplicate_session_$DEVICEID" "rtc_cpu_consumption:${rtc_cpu_consumption},cpu_before_install:${cpu_before_install},cpu_after_install:${cpu_after_install},install_time:${timetaken},install_attempts:${attempts},event_name:AndroidInstall,status:success$TEAM_PRODUCT_STR" "$EVENT_TYPE" "false" &
        if [[ $IS_APP_AUTOMATE = "true" ]]; then
          /usr/local/.browserstack/mobile/common/push_to_zombie.rb "android" "app-install-metrics" "$attempts" "$DEVICE_NAME"  "$install_with_consent:$user_consent_happened" "$DEVICEID" "$SESSION_ID" "$USER_ID" "$DEVICE_VERSION" "$package" "$RETRY_COUNT" "SUCCESS" "$timetaken_ui_automation" "$timetaken_adb_push" &
        fi
        if [[ "$install_experiment_enabled" == "true" ]]; then
          adb -s $DEVICEID shell "rm /data/local/tmp/app-${DEVICEID}.apk" &
        fi
      fi
      return ;;
    *TIMEDOUT*)
      # Timeout while installing app, retry
      if [[ $attempts -lt $MAX_INSTALL_RETRIES ]]
      then
        if [[ $(adb -s $DEVICEID shell pm list packages | grep -c "${package}" ) -gt 0 ]]; then
          echo "App installed on device"
          $BS_DIR/mobile-common/common/eds.rb "/tmp/duplicate_session_$DEVICEID" "rtc_cpu_consumption:${rtc_cpu_consumption},cpu_before_install:${cpu_before_install},cpu_after_install:${cpu_after_install},install_time:${timetaken},install_attempts:${attempts},event_name:AndroidInstall,status:success$TEAM_PRODUCT_STR" "$EVENT_TYPE" "false" &
          if [[ $IS_APP_AUTOMATE = "true" ]]; then
            /usr/local/.browserstack/mobile/common/push_to_zombie.rb "android" "app-install-metrics" "$attempts" "$DEVICE_NAME"  "$install_with_consent:$user_consent_happened" "$DEVICEID" "$SESSION_ID" "$USER_ID" "$DEVICE_VERSION" "$package" "$RETRY_COUNT" "SUCCESS_AFTER_TIMEOUT" "$timetaken_ui_automation" "$timetaken_adb_push" &
          fi

          if [[ "$install_experiment_enabled" == "true" ]]; then
            adb -s $DEVICEID shell "rm /data/local/tmp/app-${DEVICEID}.apk" &
          fi
        else
          install_app "$package" "$adb_options" $((attempts + 1)) "false" "${start}" "$download_path"
        fi
      else
        # Max retry reached
        notify_pusher "App install failed: INSTALL_TIMEDOUT" &
        cls_log_message "app_install_error: INSTALL_TIMEDOUT - ${package}" &
        $BS_DIR/mobile-common/common/eds.rb "/tmp/duplicate_session_$DEVICEID" "rtc_cpu_consumption:${rtc_cpu_consumption},cpu_before_install:${cpu_before_install},cpu_after_install:${cpu_after_install},event_name:AndroidInstall,status:failed,install_attempts:${attempts},msg:INSTALL_FAILED_TIMEDOUT$TEAM_PRODUCT_STR" "$EVENT_TYPE" "false" &
        exit 1
      fi
      return ;;
    *INSTALL_FAILED_ALREADY_EXISTS*)
      if [[ $(grep -Fxc "${package}" "/tmp/ALL_SYSTEM_AND_UPLOADED_APPS_$DEVICEID") -eq 0 ]]; then
        if [ "$(is_package_installed_from_playstore "$package")" == "yes" ]; then
          notify_pusher "app_upgrade" &
          cls_log_message "App upgrade for ${package}" &
          if [ "$IS_APP_ACCESSIBILITY" != "true" ]; then
            $BS_DIR/mobile-common/common/eds.rb "/tmp/duplicate_session_$DEVICEID" "app_upgrade_count:1" "app_live_test_sessions"
          fi
          install_app "$package" "$adb_options -r" $((attempts + 1)) "false" "${start}" "$download_path"
        else
          reinstall_and_notify "$package" "$adb_options" $((attempts + 1)) "false" "${start}" "$download_path"
        fi
      elif [ "$attempts" -gt 0 ]; then
        # initial attempts timedout
        echo "App installed on device"
        $BS_DIR/mobile-common/common/eds.rb "/tmp/duplicate_session_$DEVICEID" "rtc_cpu_consumption:${rtc_cpu_consumption},cpu_before_install:${cpu_before_install},cpu_after_install:${cpu_after_install},install_time:${timetaken},install_attempts:${attempts},event_name:AndroidInstall,status:success$TEAM_PRODUCT_STR" "$EVENT_TYPE" "false" &
        if [[ "$install_experiment_enabled" == "true" ]]; then
          adb -s $DEVICEID shell "rm /data/local/tmp/app-${DEVICEID}.apk" &
        fi
      else
        notify_pusher "App already installed on device: INSTALL_FAILED_ALREADY_EXISTS___INSTALL_FAILED_ALREADY_EXISTS" &
        cls_log_message "app_install_error: INSTALL_FAILED_ALREADY_EXISTS - ${package}" &
        end=$(($(date +%s%N)/1000000))
        timetaken=$((end-start))
        $BS_DIR/mobile-common/common/eds.rb "/tmp/duplicate_session_$DEVICEID" "rtc_cpu_consumption:${rtc_cpu_consumption},cpu_before_install:${cpu_before_install},cpu_after_install:${cpu_after_install},install_time:${timetaken},event_name:AndroidInstall,status:failed,install_attempts:${attempts},msg:INSTALL_FAILED_ALREADY_EXISTS$TEAM_PRODUCT_STR" "$EVENT_TYPE" "false" &
        exit 1
      fi
      return ;;
    *INSTALL_PARSE_FAILED_NO_CERTIFICATES*)
      # try with signing and zip aligning the app if it was skipped earlier
      if [[ $SKIP_APK_SIGNING_FLOW == "true" ]]; then
        echo "Signing and zipaligning the app - fallback" 
        SKIP_APK_SIGNING_FLOW="false"
        sign_and_install_app
      else
        notify_pusher "App install failed___INSTALL_PARSE_FAILED_NO_CERTIFICATES" &
        cls_log_message "app_install_error: INSTALL_PARSE_FAILED_NO_CERTIFICATES - ${package}" &
        end=$(($(date +%s%N)/1000000))
        timetaken=$((end-start))
        $BS_DIR/mobile-common/common/eds.rb "/tmp/duplicate_session_$DEVICEID" "rtc_cpu_consumption:${rtc_cpu_consumption},cpu_before_install:${cpu_before_install},cpu_after_install:${cpu_after_install},install_time:${timetaken},event_name:AndroidInstall,status:failed,install_attempts:${attempts},skip_apk_signing:${SKIP_APK_SIGNING_FLOW},msg:INSTALL_PARSE_FAILED_NO_CERTIFICATES$TEAM_PRODUCT_STR" "$EVENT_TYPE" "false" &
        exit 1
      fi
      ;;
    *INSTALL_FAILED_OLDER_SDK*)
      notify_pusher "App install failed: INSTALL_FAILED_OLDER_SDK___INSTALL_FAILED_OLDER_SDK" &
      cls_log_message "app_install_error: INSTALL_FAILED_OLDER_SDK - ${package}" &
      end=$(($(date +%s%N)/1000000))
      timetaken=$((end-start))
      $BS_DIR/mobile-common/common/eds.rb "/tmp/duplicate_session_$DEVICEID" "rtc_cpu_consumption:${rtc_cpu_consumption},cpu_before_install:${cpu_before_install},cpu_after_install:${cpu_after_install},install_time:${timetaken},event_name:AndroidInstall,status:failed,install_attempts:${attempts},msg:INSTALL_FAILED_OLDER_SDK$TEAM_PRODUCT_STR" "$EVENT_TYPE" "false" &
      exit 1 ;;
    *INSTALL_FAILED_TEST_ONLY*) # User cannot install apps with android:testOnly="true" in manifest
      if [[ -z "${adb_options// }" ]]; then
        adb_options="-t"
      else
        adb_options+=" -t"
      fi
      install_app "$package" "$adb_options" $((attempts + 1)) "false" "${start}" "$download_path"
      return ;;
    *INSTALL_FAILED_VERSION_DOWNGRADE*) # user cannot install an app with lower version_code
      if [[ $attempts -lt $MAX_INSTALL_RETRIES ]] && [ -f $STATE_FILES_DIR/dedicated_cleanup_$DEVICEID ]; then
        adb -s $DEVICEID uninstall $package
        install_app "$package" "$adb_options" $((attempts + 1)) "false" "${start}" "$download_path"
      else
        notify_pusher "App already installed on device: INSTALL_FAILED_VERSION_DOWNGRADE___INSTALL_FAILED_VERSION_DOWNGRADE" &
        cls_log_message "app_install_error: INSTALL_FAILED_VERSION_DOWNGRADE - ${package}" &
        end=$(($(date +%s%N)/1000000))
        timetaken=$((end-start))
        $BS_DIR/mobile-common/common/eds.rb "/tmp/duplicate_session_$DEVICEID" "rtc_cpu_consumption:${rtc_cpu_consumption},cpu_before_install:${cpu_before_install},cpu_after_install:${cpu_after_install},install_time:${timetaken},event_name:AndroidInstall,status:failed,install_attempts:${attempts},msg:INSTALL_FAILED_VERSION_DOWNGRADE$TEAM_PRODUCT_STR" "$EVENT_TYPE" "false" &
        exit 1
      fi
      return ;;
    *INSTALL_FAILED_NO_MATCHING_ABIS*) # user cannot install an app on a device with a different CPU architecture it was built on
      notify_pusher "App install failed: INSTALL_FAILED_NO_MATCHING_ABIS___INSTALL_FAILED_NO_MATCHING_ABIS" &
      cls_log_message "app_install_error: INSTALL_FAILED_NO_MATCHING_ABIS - ${package}" &
      end=$(($(date +%s%N)/1000000))
      timetaken=$((end-start))
      $BS_DIR/mobile-common/common/eds.rb "/tmp/duplicate_session_$DEVICEID" "rtc_cpu_consumption:${rtc_cpu_consumption},cpu_before_install:${cpu_before_install},cpu_after_install:${cpu_after_install},install_time:${timetaken},event_name:AndroidInstall,status:failed,install_attempts:${attempts},msg:INSTALL_FAILED_NO_MATCHING_ABIS$TEAM_PRODUCT_STR" "$EVENT_TYPE" "false" &
      exit 1 ;;
    *INSTALL_PARSE_FAILED_NOT_APK*)
      notify_pusher "App install failed: INSTALL_PARSE_FAILED_NOT_APK___INSTALL_PARSE_FAILED_NOT_APK" &
      cls_log_message "app_install_error: INSTALL_PARSE_FAILED_NOT_APK - ${package}" &
      end=$(($(date +%s%N)/1000000))
      timetaken=$((end-start))
      $BS_DIR/mobile-common/common/eds.rb "/tmp/duplicate_session_$DEVICEID" "rtc_cpu_consumption:${rtc_cpu_consumption},cpu_before_install:${cpu_before_install},cpu_after_install:${cpu_after_install},install_time:${timetaken},event_name:AndroidInstall,status:failed,install_attempts:${attempts},msg:INSTALL_PARSE_FAILED_NOT_APK$TEAM_PRODUCT_STR" "$EVENT_TYPE" "false" &
      exit 1 ;;
    *INSTALL_PARSE_FAILED_INCONSISTENT_CERTIFICATES*)
      # only for devices <= android 5, because trying to install app with different certificates throws
      # INSTALL_FAILED_ALREADY_EXISTS on these android version, and that case is handled in INSTALL_FAILED_ALREADY_EXISTS above
      notify_pusher "App install failed: INSTALL_PARSE_FAILED_INCONSISTENT_CERTIFICATES___INSTALL_PARSE_FAILED_INCONSISTENT_CERTIFICATES" &
      cls_log_message "app_install_error: INSTALL_PARSE_FAILED_INCONSISTENT_CERTIFICATES - ${package}" &
      end=$(($(date +%s%N)/1000000))
      timetaken=$((end-start))
      $BS_DIR/mobile-common/common/eds.rb "/tmp/duplicate_session_$DEVICEID" "rtc_cpu_consumption:${rtc_cpu_consumption},cpu_before_install:${cpu_before_install},cpu_after_install:${cpu_after_install},install_time:${timetaken},event_name:AndroidInstall,status:failed,install_attempts:${attempts},msg:INSTALL_PARSE_FAILED_INCONSISTENT_CERTIFICATES$TEAM_PRODUCT_STR" "$EVENT_TYPE" "false" &
      exit 1 ;;
    *INSTALL_FAILED_UPDATE_INCOMPATIBLE*)
      if ! device_is_a bsrun_device; then
        # might be due to patched application on network logs
        reinstall_and_notify "$package" "$adb_options" $((attempts + 1)) "false" "${start}" "$download_path"
      # for devices >= android 6
      elif [[ $(grep -Fxc "${package}" "/tmp/ALL_SYSTEM_AND_UPLOADED_APPS_$DEVICEID") -eq 0 ]]; then
        if [ "$(is_package_installed_from_playstore "$package")" == "yes" ]; then
          notify_pusher "App install failed: INSTALL_PARSE_FAILED_INCONSISTENT_CERTIFICATES___INSTALL_PARSE_FAILED_INCONSISTENT_CERTIFICATES" &
          cls_log_message "app_install_error: INSTALL_PARSE_FAILED_INCONSISTENT_CERTIFICATES - ${package}" &
          end=$(($(date +%s%N)/1000000))
          timetaken=$((end-start))
          $BS_DIR/mobile-common/common/eds.rb "/tmp/duplicate_session_$DEVICEID" "rtc_cpu_consumption:${rtc_cpu_consumption},cpu_before_install:${cpu_before_install},cpu_after_install:${cpu_after_install},install_time:${timetaken},event_name:AndroidInstall,status:failed,install_attempts:${attempts},msg:INSTALL_PARSE_FAILED_INCONSISTENT_CERTIFICATES$TEAM_PRODUCT_STR" "$EVENT_TYPE" "false" &
        else
          reinstall_and_notify "$package" "$adb_options" $((attempts + 1)) "false" "${start}" "$download_path"
        fi
      else
        notify_pusher "App install failed: INSTALL_PARSE_FAILED_INCONSISTENT_CERTIFICATES___INSTALL_PARSE_FAILED_INCONSISTENT_CERTIFICATES" &
        cls_log_message "app_install_error: INSTALL_PARSE_FAILED_INCONSISTENT_CERTIFICATES - ${package}" &
        end=$(($(date +%s%N)/1000000))
        timetaken=$((end-start))
        $BS_DIR/mobile-common/common/eds.rb "/tmp/duplicate_session_$DEVICEID" "rtc_cpu_consumption:${rtc_cpu_consumption},cpu_before_install:${cpu_before_install},cpu_after_install:${cpu_after_install},install_time:${timetaken},event_name:AndroidInstall,status:failed,install_attempts:${attempts},msg:INSTALL_PARSE_FAILED_INCONSISTENT_CERTIFICATES$TEAM_PRODUCT_STR" "$EVENT_TYPE" "false" &
        exit 1
      fi
      return ;;
    *)
      if [ ${#adb_install_out} -gt 100 ]; then
        adb_install_out="${adb_install_out: -100}";
      fi
      # Do nothing
      cls_log_message "app_install_retry: Install failed, Attempts(${attempts}) - ${package} ${adb_install_out}" &
      if [[ "$attempts" -ge 9 ]]; then
        notify_pusher "App install failed___unknown___${adb_install_out}" &
        end=$(($(date +%s%N)/1000000))
        timetaken=$((end-start))
        if is_device_pointed_to_prod && [[ $IS_APP_AUTOMATE != "true" ]] && [[ $IS_APP_ACCESSIBILITY != "true" ]]; then
          $BS_DIR/mobile/common/push_to_zombie.rb "android" "app-live-apk-install-failed" "package:${package},adb_install_out:${adb_install_out},instrumentation_type:${INSTRUMENTATION_TYPES}" "android" "${timetaken}" "$DEVICEID" "$SESSION_ID" "$USER_ID" "$DEVICE_VERSION"
          $BUNDLE exec ruby "$BS_DIR/mobile/common/hooter.rb" "send_uptime_metric" "$USERNAME" "app-live" "apk_install_failure"
        fi
        $BS_DIR/mobile-common/common/eds.rb "/tmp/duplicate_session_$DEVICEID" "rtc_cpu_consumption:${rtc_cpu_consumption},cpu_before_install:${cpu_before_install},cpu_after_install:${cpu_after_install},install_time:${timetaken},event_name:AndroidInstall,status:failed,install_attempts:${attempts}$TEAM_PRODUCT_STR" "$EVENT_TYPE" "false" &
      fi
      install_app "$package" "$adb_options" $((attempts + 1)) "false" "${start}" "$download_path"
  esac
}

install_apks() {
  session_id="$1"
  user_id=$2
  download_path="$3"
  bundletool_install_out=$(timeout $INSTALL_TIMEOUT $JAVA_8_HOME_PATH/bin/java -jar $BUNDLETOOL_PATH install-apks --apks=$download_path --device-id=$DEVICEID 2>&1)
  retVal=$?
  echo "$bundletool_install_out"
  if [ $retVal -e 0 ]
  then
    return 0
  elif [ $retVal -ne 0 ]
  then
    case "$bundletool_install_out" in
      *IncompatibleDeviceException*)
        exit 1
        return ;;
      *CommandExecutionException*)
        exit 1
        return ;;
    esac
  fi
}

install_instant_app() {
  session_id="$1"
  user_id=$2
  download_path="$3"
  install_out=$(timeout $INSTALL_TIMEOUT adb -s $DEVICEID install -r -t --instantapp "${download_path}" 2>&1)
  retVal=$?
  if [ $retVal -eq 0 ]; then
    return 0
  elif [ $retVal -ne 0 ]; then
    case "$install_out" in
      *IncompatibleDeviceException*)
        exit 1 ;;
      *CommandExecutionException*)
        exit 1 ;;
      *)
        echo "Unexpected error occurred in install_instant_app : $install_out" ;;
    esac
  fi
}

is_app_unsigned_by_apksigner() {
  # sample output of apksigner verify --verbose:
    # Verified using v1 scheme (JAR signing): false
    # Verified using v2 scheme (APK Signature Scheme v2): false
    # Verified using v3 scheme (APK Signature Scheme v3): false
  if [[ $is_app_unsigned_by_apksigner_status != -1 ]]; then
    return $is_app_unsigned_by_apksigner_status
  else
    if [[ $($BUILD_TOOLS_PATH/apksigner verify --print-certs --verbose "$1"| grep -c -e "(APK Signature Scheme v[0-9]): true") -eq 0 ]]; then
      echo 0
      return 0
    fi
    echo 1
    return 1
  fi
}

#verifies if app is jarsigned(v1 scheme) or not, using apksigner
#this will prevent jarsigner detecting app as signed with `jar contains entries whose certificate chain is invalid` error
is_app_unsigned_by_v1() {
  if [[ $($BUILD_TOOLS_PATH/apksigner verify -verbose --print-certs "$1" | grep -c -e "(JAR signing): true") -eq 0 ]]; then
    return 0
  fi
  return 1
}

is_app_unsigned() {
  if [[ $is_app_unsigned_status != -1 ]]; then
      return $is_app_unsigned_status
  else
    if [[ $($JARSIGNER -verify -verbose -certs "$1" | grep -c "^jar is unsigned") -eq 1 ]]; then
      echo 0
      return 0
    fi
    echo 1
    return 1
  fi
}

sign_app() {
  start=$(($(date +%s%N)/1000000))
  jarsigning_status=false
  if [[ ${enable_apksigner} == "true" ]]; then
    if [[ $force_resign == "true" ]] || ($(is_app_unsigned_by_v1 "${download_path}") && $(is_app_unsigned_by_apksigner "${download_path}")) ; then
       #using same config as jarsigner
       signer_config=$(cat "${CONFIG}/static_conf.json" | jq -r '.jarsigner ' | tr -d "\r\n")
       apksigner_params=$(echo "$signer_config" | jq -r ' " --ks \(.key_store) --ks-key-alias \(.key_alias) --ks-pass pass:\(.store_pass) --key-pass pass:\(.key_pass) "' \
         | tr -d "\r\n")
       #signing app with apksigner
       ${BUILD_TOOLS_PATH}/apksigner sign ${apksigner_params} "${download_path}"
       jarsigning_status=true
    fi
  else
    if $(is_app_unsigned "${download_path}"); then
      jarsigning_status=true
      signer_config=$(cat "${CONFIG}/static_conf.json" | jq -r '.jarsigner ' | tr -d "\r\n")
      jarsigner_params=$(echo "$signer_config" | jq -r ' " -keystore \(.key_store) -storepass \(.store_pass) -keypass \(.key_pass) "' \
        | tr -d "\r\n")

      key_alias=$(echo "$signer_config" | jq -r ".key_alias" | tr -d "\r\n")
      $JARSIGNER -sigalg SHA1withRSA -digestalg SHA1 $jarsigner_params "${download_path}" "$key_alias"
    fi
  fi
  end=$(($(date +%s%N)/1000000))
  timetaken=$((end-start))
  $BS_DIR/mobile-common/common/eds.rb "/tmp/duplicate_session_$DEVICEID" "jarsigning_status:${jarsigning_status},jarsigning_time:${timetaken},event_name:AndroidSigning$TEAM_PRODUCT_STR" "$EVENT_TYPE" "false"
}

verify_zip_aligned_app() {
 TIMEOUT=300
 infile="$1"
 if [[ $(timeout $TIMEOUT $ZIPALIGN_PATH -c -v -p 4 $infile | grep -c "Verification succesful") -eq 0 ]]; then
    return 0
 fi
 return 1
}

zip_align_app() {
 START_TIME=$(date +"%s")
 TIMEOUT=300
 infile="$1"
 outfile="$2"

 echo "Zipaliging app : $infile to $outfile"
 timeout $TIMEOUT $ZIPALIGN_PATH -f -v -p 4 $infile $outfile
 retVal=$?

 if [ $retVal -eq 124 ]
 then
   #timeout occured
   echo "$SESSION_ID Zipaliging app timedout: $infile to $outfile"
   /usr/local/.browserstack/mobile/common/push_to_zombie.rb "android" "app-zipalign-timeout" "timeout-error" "android" "$TIMEOUT" "$DEVICEID" "$SESSION_ID" "$USER_ID" "$DEVICE_VERSION" --is_app_accessibility "$IS_APP_ACCESSIBILITY"
   return 124
 elif [ $retVal -ne 0 ]
 then
   TIME_TAKEN=$(($(date +"%s")-$START_TIME))
   if $(verify_zip_aligned_app "$outfile"); then
    echo "$SESSION_ID Zipaliging app error: $outfile"
    /usr/local/.browserstack/mobile/common/push_to_zombie.rb "android" "app-zipalign-error" "error" "android" "$TIME_TAKEN" "$DEVICEID" "$SESSION_ID" "$USER_ID" "$DEVICE_VERSION" --is_app_accessibility "$IS_APP_ACCESSIBILITY"
    return 1
   fi
 fi

 TIME_TAKEN=$(($(date +"%s")-$START_TIME))
 echo "$SESSION_ID Zipaliging app done: $infile to $outfile"
 /usr/local/.browserstack/mobile/common/push_to_zombie.rb "android" "app-zipalign-done" "success" "android" "$TIME_TAKEN" "$DEVICEID" "$SESSION_ID" "$USER_ID" "$DEVICE_VERSION" --is_app_accessibility "$IS_APP_ACCESSIBILITY"
 mv $outfile $infile
 return 0
}

check_if_privoxy_loaded() {
  start_time=$(($(date +%s%N)/1000000))
  timetaken=0
  retry_attempt=0
  success_status="false"
  while [ $timetaken -le 9000 ]
  do
    retry_attempt=$(( retry_attempt+1 ))
    resp=$(curl -m 2 -x 127.0.0.1:$PROXY_PORT -A "Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:59.0) Gecko/20100101 Firefox/59.0 Android Zotak" http://ping-local.browserstack.com/local-debug/http)
    end_time=$(($(date +%s%N)/1000000))
    timetaken=$((end_time-start_time))
    if [[ "$resp" == "127.0.0.1" ]]; then
      success_status="true"
      break
    fi
    sleep .5
  done
  session_id=$(cat /tmp/app_live_logs_params_$DEVICEID | cut -d " " -f2)
  /usr/local/.browserstack/mobile/common/push_to_zombie.rb "android" "privoxy-loaded" "$success_status" "android" "timetaken:${timetaken},retry_count:${retry_attempt}" "$DEVICEID" "$session_id" --is_app_accessibility "$IS_APP_ACCESSIBILITY"
}

set_package_and_launch_activity_for_app(){
  if [[ "$APP_HASHED_ID" == "play_store" ]]; then
    # Set variables to launch PlayStore
    app_launch_eds_event_name="PlayStoreLaunch"
    package="com.android.vending"
    launch_activity="com.google.android.finsky.activities.MainActivity"
  elif [[ "$APP_HASHED_ID" == "google_map" ]]; then
    # Set variables to launch Google maps
    app_launch_eds_event_name="GoogleMapsLaunch"
    package="com.google.android.apps.maps"
    launch_activity="com.google.android.maps.MapsActivity"
  else
    # Should never reach here
    echo "${APP_HASHED_ID} should either be play_store OR google_map"
  fi
}

# This function is responsible for enabling the async app launch flow which downloads and installs the user app at the start of the session, early on, to reduce app launch time
# App launch is done after do_pre_start to avoid any conflicts with automation (like MediaProjection popup handling)
# Need to ensure all automations are handled before launching the user app
should_use_async_app_launch_setup() {
  if [[ "$ASYNC_APP_LAUNCH_SETUP_ENABLED" != "true" || 
        "$APP_HASHED_ID" == "play_store" || 
        "$APP_HASHED_ID" == "google_map" ||
        "$APP_HASHED_ID" == "no_app" || 
        "$relaunch_flow" == "true" || 
        ( "$IS_APP_ACCESSIBILITY" == "true" && -z "$APP_HASHED_ID" ) ]]; then
    return 1
  fi

  # Explicitly check if ASYNC_APP_LAUNCH_SETUP_ENABLED is "true" before returning 0
  if [[ "$ASYNC_APP_LAUNCH_SETUP_ENABLED" == "true" ]]; then
    return 0
  fi

  # Default case (this should never be reached, but added for clarity)
  return 1
}

handle_passcode() {
  if [ "$IS_PASSCODE_TOGGLED" == "true" ]; then
    notify_event "Passcode_Started,${SESSION_ID}"
    $BUNDLE exec ruby $DEVICE_PASSCODE_HELPER "set_passcode_for_applive_session" "$DEVICEID"
    IS_PASSCODE_SET=$(cat /tmp/app_live_set_passcode_result_$DEVICEID)
    rm /tmp/app_live_set_passcode_result_$DEVICEID
    if [ $IS_PASSCODE_SET == "true" ]; then
      notify_event "Passcode_Success,${SESSION_ID}"
    else
      notify_event "Passcode_Failed,${SESSION_ID}"
    fi
  fi
}

handle_app_start() {
  handle_passcode &
  if [[ "$APP_HASHED_ID" == "play_store" || "$APP_HASHED_ID" == "google_map" ]]; then
    app_launch_eds_event_name=""
    set_package_and_launch_activity_for_app
    app_start_time=$(($(date +%s%N)/1000000))
    echo "$package" > "/tmp/app_session_${DEVICEID}"
    # Checking if privoxy is loaded or not in case of play store launch
    check_if_privoxy_loaded
    if [[ "$switch_app" == "false" ]]; then
      # Start device logger to setup streamLogs watcher before frontend sends stream_logs event
      notify_device_logger
    fi

    # This sleep is required to notify device logger for start and update seperately
    # else only one update event is fired
    sleep 1
    notify_event "launching_${APP_HASHED_ID}" &
    launch_app
    app_end_time=$(($(date +%s%N)/1000000))
    app_time_taken=$((app_end_time-app_start_time))
    $BS_DIR/mobile-common/common/eds.rb "/tmp/duplicate_session_$DEVICEID" "${APP_HASHED_ID}_launch_time:${app_time_taken},event_name:${app_launch_eds_event_name}$TEAM_PRODUCT_STR" "$EVENT_TYPE" "false"
  else
    set_app_info
    # this is to disable playstore if and when we are facing spam and the feature is blocked on rails
    if [[ "$play_store_disabled" == "true" ]]; then
      play_store $DEVICEID disable &
    fi
    if [[ "$relaunch_flow" == "true" ]]; then
      echo "Starting Relaunch Flow"
      if ! device_is_a bsrun_device; then
        echo "Relaunch with patched APK ${USERS_PATCHED_APP_PATH}"
        installed_app_path=$(adb -s $DEVICEID shell pm path $package | sed -e 's/package://')
        installed_app_md5=$(adb -s $DEVICEID shell md5sum ${installed_app_path} | sed -e "s|$installed_app_path||")
        if [[ $IS_NETWORK_LOGS_ENABLED == "true" ]] && [ -f "$USERS_PATCHED_APP_PATH" ]; then
          app_to_install_md5_sum=$(md5sum ${USERS_PATCHED_APP_PATH} | sed -e "s|$USERS_PATCHED_APP_PATH||")
        else
          app_to_install_md5_sum=$(md5sum ${APP_PATH} | sed -e "s|$APP_PATH||")
        fi
        echo "INSTALLED APP MD5 $installed_app_md5"
        echo "TO BE INSTALLED MD5 $app_to_install_md5_sum"
        # check if installed app is same as to be installed app, if yes then don't install
        if [[ "$app_to_install_md5_sum" == "$installed_app_md5" ]]; then
          kill_and_launch_app
        else
          adb -s $DEVICEID uninstall $package
          if [[ ! -z "$launch_activity" ]]; then
            notify_event "show_restart_device_logs"
          fi
          start_app
        fi
      else
        kill_and_launch_app
      fi
    else
      if [[ "$IS_APP_ACCESSIBILITY" == "true" && -z "$APP_HASHED_ID" ]]; then
            echo "App accessibility session enabled and app hash id not set. Skipping download and launch."
            notify_device_logger
            return
      fi
      if [[ "$APP_HASHED_ID" == "no_app" ]]; then
        echo "No app session enabled. Skipping download and launch."
        return
      fi

      if ! should_use_async_app_launch_setup; then
        download_app && start_app
      fi
    fi
  fi
}

kill_and_launch_app() {
  kill_all_user_apps
  if [[ "$switch_app" == "false" ]]; then
    # Start device logger to setup streamLogs watcher before frontend sends stream_logs event
    notify_device_logger
  fi
  # This sleep is required to notify device logger for start and update seperately
  # else only one update event is fired
  sleep 1
  if [[ ! -z "$launch_activity" ]]; then
    notify_event "show_restart_device_logs"
  fi
  launch_app
}

get_all_user_apps() {
  device_version=`$timeout 5 adb -s $DEVICEID shell getprop ro.build.version.release`
  if [[ "${device_version:0:1}" -ge "7" ]]; then
    all_apps=`$timeout 5 adb -s $DEVICEID shell cmd package list packages -u -i -3`
    if [[ "$1" == "write_all_apps_file" ]]; then
      $timeout 5 adb -s $DEVICEID shell cmd package list packages | awk -F":" '{print $2}' > "/tmp/ALL_SYSTEM_AND_UPLOADED_APPS_$DEVICEID"
    fi
  else
    all_apps=`$timeout 5 adb -s $DEVICEID shell pm list packages -u -i -3`
    if [[ "$1" == "write_all_apps_file" ]]; then
      $timeout 5 adb -s $DEVICEID shell pm list packages | awk -F":" '{print $2}' > "/tmp/ALL_SYSTEM_AND_UPLOADED_APPS_$DEVICEID"
    fi
  fi
}

kill_all_user_apps() {
  get_all_user_apps "dont_write_all_apps_file"
  counter=0
  all_apps=`echo "$all_apps" | awk -F":| " '{print $2}'`
  if [[ ! -z "$all_apps" ]] && [ -s "/tmp/ALL_APPS_${DEVICEID}" ]; then
    apps_to_kill=`diff <(echo "$all_apps") "/tmp/ALL_APPS_${DEVICEID}" | grep "^<" | cut -c3-`
    force_stop_cmds=""
    for app_package_name in $(echo $apps_to_kill | sed "s/\n/ /g")
    do
      force_stop_cmds+="am force-stop $app_package_name;"
      counter=$((counter+1))
    done
    $timeout 30 adb -s $DEVICEID shell <<__EOF
    $force_stop_cmds
    exit
__EOF
    echo "$counter"
  fi
}

uninstall_all_user_apps() {
  get_all_user_apps "dont_write_all_apps_file"
  uninstall_counter=0
  all_apps=`echo "$all_apps" | awk -F":| " '{print $2}'`
  get_device_model
  get_os_version
  session_id=$(cat /tmp/app_live_logs_params_$DEVICEID | cut -d " " -f2)
  if [[ ! -z "$all_apps" ]] && [ -s "/tmp/ALL_APPS_${DEVICEID}" ]; then
    apps_to_uninstall=`diff <(echo "$all_apps") "/tmp/ALL_APPS_${DEVICEID}" | grep "^<" | cut -c3-`
    uninstall_counter=`echo $apps_to_uninstall | wc -w`
    if [ $uninstall_counter -eq 0 ]; then
      $BS_DIR/mobile/common/push_to_zombie.rb "android" "app-live-uninstallation-no-apps-found" "no-user-apps, uninstall_counter:${uninstall_counter}, apps_to_uninstall:${apps_to_uninstall}" "$DEVICEID" "${uninstall_counter}" "${device_model}" "${session_id}" "" "${OS_VERSION}" --is_app_accessibility "$IS_APP_ACCESSIBILITY"
      notify_pusher "uninstallation_no_apps_found"
      echo "There is no user app in the device to uninstall"
      return 0
    fi
    clear_data=`$timeout 30 echo $apps_to_uninstall | xargs -r -n1 adb -s $DEVICEID pm clear`
    resp=`$timeout 30 echo "$apps_to_uninstall" | xargs -r -n1 adb -s $DEVICEID uninstall`
    successful_uninstall=`grep -i "success" <<< $resp | wc -w`
    if [ $successful_uninstall -eq $uninstall_counter ]; then
      $BS_DIR/mobile/common/push_to_zombie.rb "android" "app-live-uninstallation-done" "uninstallation-done, uninstall_counter:${uninstall_counter}, apps_to_uninstall:${apps_to_uninstall}" "$DEVICEID" "${uninstall_counter}" "${device_model}" "${session_id}" "" "${OS_VERSION}" --is_app_accessibility "$IS_APP_ACCESSIBILITY"
      notify_pusher "uninstallation_done"
      echo "Successfully Uninstalled All User Apps"
    else
      $BS_DIR/mobile/common/push_to_zombie.rb "android" "app-live-uninstallation-failed" "uninstallation-failed, uninstall_counter:${uninstall_counter}, successful_uninstall:${successful_uninstall}, apps_to_uninstall:${apps_to_uninstall}" "$DEVICEID" "${uninstall_counter}" "${device_model}" "${session_id}" "" "${OS_VERSION}" --is_app_accessibility "$IS_APP_ACCESSIBILITY"
      notify_pusher "uninstallation_failed"
      echo "Something went wrong while uninstalling"
    fi
    echo "$uninstall_counter"
  fi
}

upload_log_file() {
  LOG_FILE=$1
  FILE_NAME=$2

  FILE_SIZE_KB=$(expr $(ls -l $LOG_FILE | awk '{print $5}') / 1024)

  s3url="https://s3.amazonaws.com/$AWS_BUCKET/$SESSION_ID/$FILE_NAME"
  START_UPLOAD_TIME=$(date +"%s")
  timeout 3001 ruby "$BS_DIR/mobile/android/scripts/upload_to_s3.rb" "$AWS_KEY" "$AWS_SECRET" "text/plain" "$LOG_FILE" "public-read" "$s3url" "" 3000
  # $timeout 30000 /usr/local/bin/s3curl --id=$AWS_KEY --key=$AWS_SECRET --contentType "text/plain" --put=$LOG_FILE -- -H "x-amz-acl: public-read" $s3url
  if [ $? -ne 0 ]
  then
    #timeout occured
    /usr/local/.browserstack/mobile/common/push_to_zombie.rb "android" "app-live-logs-upload-timeout-${FILE_NAME}" "timeout-error" "android" "$FILE_SIZE_KB" "$DEVICEID" "$SESSION_ID" --is_app_accessibility "$IS_APP_ACCESSIBILITY"
  else
    TIME_TAKEN=$(($(date +"%s")-$START_UPLOAD_TIME))
    /usr/local/.browserstack/mobile/common/push_to_zombie.rb "android" "app-live-logs-upload-done-${FILE_NAME}" "success" "android" "$TIME_TAKEN" "$DEVICEID" "$SESSION_ID" --is_app_accessibility "$IS_APP_ACCESSIBILITY"
  fi

  rm -rf $LOG_FILE
}

full_reset_app() {
  echo "Inside full_reset_app"
  remove_app "$1"
  cls_log_message "Uninstalled ${package} for reinstallation." &
  install_app "$1" "$2" $3 "" "" "$4"
}

# Global variable used in common to determine what was the original function
COMPONENT=$ACTION
case $ACTION in
    start_app )
      reset_bash_instrumentation

      mark_event_start "total_bash_start_time"

      mark_event_start "set_use_rtc_app"
      rtc_app_set_use_package $USE_RTC_APP
      mark_event_end "set_use_rtc_app"

      INSTALL_TIMEOUT="${18}"
      initial_cpu_usage=$(cpuUsageForDevice $DEVICEID)
      initial_uptime=$(androidDeviceUptime $DEVICEID)
      enable_apksigner="${19}"

      mark_event_start "adb_device_check"
      do_adb_device_check
      mark_event_end "adb_device_check"

      mark_event_start "ensure_screen_unlock"
      rm -f "/var/log/browserstack/app_log_${DEVICEID}.log"
      touch "/var/log/browserstack/app_log_${DEVICEID}.log"
      if [ "$AUDIO" == "true" ]; then
        ensure_screen_is_unlocked
      else
        (ensure_screen_is_unlocked && handle_app_start) &
      fi
      mark_event_end "ensure_screen_unlock"

      mark_event_start "forward_debugger_port"
      forward_debugger_port
      mark_event_end "forward_debugger_port"

      mark_event_start "enable_play_services"
      play_services $DEVICEID "enable" &
      mark_event_end "enable_play_services"

      mark_event_start "kill_all_user_apps"
      increase_media_sound &
      already_active_session=0
      # forward_debugger_port
      if [ -f $STATE_FILES_DIR/session_$DEVICEID ]; then
        already_active_session=1
        adb -s $DEVICEID pull "$MOBILE_RTC_CONFIG" "/tmp/last_rtc_service_$DEVICEID"
        remove_flag_files
        kill_all_user_apps
      else
        touch $STATE_FILES_DIR/session_$DEVICEID
        rm /tmp/app_session_$DEVICEID
        cp $CONFIG/rtc_service_$DEVICEID $STATE_FILES_DIR/session_$DEVICEID
        adb -s $DEVICEID shell <<__EOF
        if [[ ! -d $SESSION_DIR ]]; then
          mkdir -p $SESSION_DIR
        fi
        touch $APP_LIVE_TESTING_FILE
        input keyevent 3
        exit
__EOF
        get_all_user_apps "write_all_apps_file"
        echo "$all_apps" | awk -F":| " '{print $2}' > "/tmp/ALL_APPS_$DEVICEID"
        echo "$all_apps" | grep -E 'installer=com.android.vending' | awk -F":| " '{print $2}' > "/tmp/PLAYSTORE_APPS_$DEVICEID"
        echo "$all_apps" | grep -E 'installer=null' | awk -F":| " '{print $2}' > "/tmp/UPLOADED_APPS_$DEVICEID"
      fi
      mark_event_end "kill_all_user_apps"

      mark_event_start "do_pre_start"
      adb -s $DEVICEID push "$CONFIG/rtc_service_$DEVICEID" "$MOBILE_RTC_CONFIG"
      # adding duplicate_session file since we remove session file in stop
      # and need session file for relevant data in cleanup.
      cp -p "$CONFIG/rtc_service_$DEVICEID" "/tmp/duplicate_session_$DEVICEID"

      services_count=$(adb -s $DEVICEID shell ps | grep "AndroidRTC\|bs_screencap\|interactions_server\|uiautomator" | wc -l)
      if [ ! -f $CONFIG/calculated_args_$DEVICEID ] || [ $services_count != 4 ] || [ ! -f /usr/local/.browserstack/auth_keys/$DEVICEID ] || [ "$ASYNC" == "false" ]; then
        # Execute pre_start things only for a fresh session and not for local switch
        # and IP geolocation toggles.
        [ $already_active_session == 0 ] && do_pre_start "$AUDIO" "$USE_RTC_APP" "$LOCALE" "$USE_RTC_APP_AUDIO" "$ENABLE_AUDIO_INJECTION" "" "$ENABLE_ROTATION_USING_APP_ORIENTATION" "$ENABLE_BS_IME" "$ENABLE_SIM" "$IS_SYNC_WEBRTC" "" "$HANDLE_MP_DURING_ROTATION"
        rtc_app_usage=$(cpuUsageForApp "$DEVICEID" "${rtc_app_use_package}")
        post_rtc_setup_cpu=$(cpuUsageForDevice $DEVICEID)
        session_id=$(cat /tmp/app_live_logs_params_$DEVICEID | cut -d " " -f2)
        eds_data="initial_uptime:${initial_uptime},initial_cpu_usage:${initial_cpu_usage},rtc_app_usage:${rtc_app_usage},post_rtc_setup_cpu:${post_rtc_setup_cpu},event_name:AndroidInstallExperiment,session_id:${session_id}$TEAM_PRODUCT_STR"
        $BS_DIR/mobile-common/common/eds.rb "/tmp/duplicate_session_$DEVICEID" $eds_data "$EVENT_TYPE" "false"
        echo $eds_data > "/tmp/install_numbers_$DEVICEID"
      else
        reset_logger
      fi
      mark_event_end "do_pre_start"

      mark_event_start "handle_app_start"
      if [ "$AUDIO" == "true" ]; then
        handle_app_start &
      fi
      mark_event_end "handle_app_start"

      enable_contacts_param=""
      if [ $ENABLE_CONTACTS_APP_ACCESS == true ]; then
        enable_contacts_param='--enable-contacts'
      fi
      echo "Enable contacts app: ${ENABLE_CONTACTS_APP_ACCESS}, ${enable_contacts_param}"

      mark_event_start "log_app_anr"
      log_app_anr &
      mark_event_end "log_app_anr"

      mark_event_start "touch_in_session_file"
      touch_in_session
      mark_event_end "touch_in_session_file"

      mark_event_start "restart_check_status_bar"
      restart_check_status_bar "app_live" "${enable_contacts_param}"
      mark_event_end "restart_check_status_bar"

      mark_event_start "push_privoxy_to_cls"
      push_privoxy_to_cls
      mark_event_end "push_privoxy_to_cls"

      mark_event_start "log_gms_version"
      log_gms_version
      mark_event_end "log_gms_version"

      mark_event_start "check_browserstack_apps"
      check_browserstack_apps
      mark_event_end "check_browserstack_apps"

      mark_event_end "total_bash_start_time"

      log_bash_instrumentation "start_app"
      ;;
    async_start_app )
      reset_bash_instrumentation

      mark_event_start "total_bash_start_time"

      mark_event_start "set_use_rtc_app"
      rtc_app_set_use_package $USE_RTC_APP
      mark_event_end "set_use_rtc_app"

      INSTALL_TIMEOUT="${18}"
      initial_cpu_usage=$(cpuUsageForDevice $DEVICEID)
      initial_uptime=$(androidDeviceUptime $DEVICEID)
      enable_apksigner="${19}"

      get_all_user_apps "write_all_apps_file"
      echo "$all_apps" | awk -F":| " '{print $2}' > "/tmp/ALL_APPS_$DEVICEID"
      echo "$all_apps" | grep -E 'installer=com.android.vending' | awk -F":| " '{print $2}' > "/tmp/PLAYSTORE_APPS_$DEVICEID"
      echo "$all_apps" | grep -E 'installer=null' | awk -F":| " '{print $2}' > "/tmp/UPLOADED_APPS_$DEVICEID"

      # Start download and install in a subshell that will signal when done
      (
          if should_use_async_app_launch_setup; then
            echo "Using async app launch setup"

            # Ensure any existing named pipe is removed before creating a new one
            if [[ -e "$ASYNC_APP_LAUNCH_SYNC_PIPE_FILE" ]]; then
                rm -f "$ASYNC_APP_LAUNCH_SYNC_PIPE_FILE"
            fi
            # Create a fresh named pipe
            mkfifo "$ASYNC_APP_LAUNCH_SYNC_PIPE_FILE"

            download_app && sign_and_install_app

            # Use timeout since echo 'done' will be a blocking call and will exit only when read command is executed
            if timeout $ASYNC_APP_LAUNCH_PIPE_TIMEOUT bash -c "echo 'done' > '$ASYNC_APP_LAUNCH_SYNC_PIPE_FILE'"; then
              echo "Successfully wrote to pipe"
            else
              echo "Write operation timed out after $ASYNC_APP_LAUNCH_PIPE_TIMEOUT seconds"
              rm "$ASYNC_APP_LAUNCH_SYNC_PIPE_FILE"
            fi
          else
            echo "Not using async app launch setup"
          fi
      ) &

      mark_event_start "adb_device_check"
      do_adb_device_check &
      mark_event_end "adb_device_check"

      mark_event_start "ensure_screen_unlock"
      rm -f "/var/log/browserstack/app_log_${DEVICEID}.log"
      touch "/var/log/browserstack/app_log_${DEVICEID}.log"
      if [ "$AUDIO" == "true" ]; then
        ensure_screen_is_unlocked &
      else
        (ensure_screen_is_unlocked && handle_app_start) &
      fi
      mark_event_end "ensure_screen_unlock"


      mark_event_start "forward_debugger_port"
      forward_debugger_port
      mark_event_end "forward_debugger_port"

      mark_event_start "enable_play_services"
      play_services $DEVICEID "enable" &
      mark_event_end "enable_play_services"
      increase_media_sound &

      already_active_session=0
      # forward_debugger_port
      if [ -f $STATE_FILES_DIR/session_$DEVICEID ]; then
        already_active_session=1
      else
        # copying session params here as they are used in audio injection and sim features in do_pre_start
        touch $STATE_FILES_DIR/session_$DEVICEID
        cp $CONFIG/rtc_service_$DEVICEID $STATE_FILES_DIR/session_$DEVICEID
      fi

      mark_event_start "do_pre_start"
      adb -s $DEVICEID push "$CONFIG/rtc_service_$DEVICEID" "$MOBILE_RTC_CONFIG"
      # adding duplicate_session file since we remove session file in stop
      # and need session file for relevant data in cleanup.
      cp -p "$CONFIG/rtc_service_$DEVICEID" "/tmp/duplicate_session_$DEVICEID"

      services_count=$(adb -s $DEVICEID shell ps | grep "AndroidRTC\|bs_screencap\|interactions_server\|uiautomator" | wc -l)
      if [ ! -f $CONFIG/calculated_args_$DEVICEID ] || [ $services_count != 4 ] || [ ! -f /usr/local/.browserstack/auth_keys/$DEVICEID ] || [ "$ASYNC" == "false" ]; then
        # Execute pre_start things only for a fresh session and not for local switch
        # and IP geolocation toggles.
        # handle MP popup before app launch
        [ $already_active_session == 0 ] && do_pre_start "$AUDIO" "$USE_RTC_APP" "$LOCALE" "$USE_RTC_APP_AUDIO" "$ENABLE_AUDIO_INJECTION" "" "$ENABLE_ROTATION_USING_APP_ORIENTATION" "$ENABLE_BS_IME" "$ENABLE_SIM" "$IS_SYNC_WEBRTC" "" "$HANDLE_MP_DURING_ROTATION"
        rtc_app_usage=$(cpuUsageForApp "$DEVICEID" "${rtc_app_use_package}")
        post_rtc_setup_cpu=$(cpuUsageForDevice $DEVICEID)
        session_id=$(cat /tmp/app_live_logs_params_$DEVICEID | cut -d " " -f2)
        eds_data="initial_uptime:${initial_uptime},initial_cpu_usage:${initial_cpu_usage},rtc_app_usage:${rtc_app_usage},post_rtc_setup_cpu:${post_rtc_setup_cpu},event_name:AndroidInstallExperiment,session_id:${session_id}$TEAM_PRODUCT_STR"
        $BS_DIR/mobile-common/common/eds.rb "/tmp/duplicate_session_$DEVICEID" $eds_data "$EVENT_TYPE" "false"
        echo $eds_data > "/tmp/install_numbers_$DEVICEID"
      else
        reset_logger
      fi
      mark_event_end "do_pre_start"

      mark_event_start "kill_all_user_apps"
      if [ $already_active_session == 1 ]; then
        adb -s $DEVICEID pull "$MOBILE_RTC_CONFIG" "/tmp/last_rtc_service_$DEVICEID"
        remove_flag_files
        kill_all_user_apps
      else
        rm /tmp/app_session_$DEVICEID
        adb -s $DEVICEID shell <<__EOF
        if [[ ! -d $SESSION_DIR ]]; then
          mkdir -p $SESSION_DIR
        fi
        touch $APP_LIVE_TESTING_FILE
        input keyevent 3
        exit
__EOF
      fi
      mark_event_end "kill_all_user_apps"

      mark_event_start "handle_app_start"
      if [ "$AUDIO" == "true" ]; then
        # app start is done in async because automation is already handled in do_pre_start
        # in case there is a conflict between MP pop up and app, we can enable SYNC_WEBRTC ( LD sync_webrtc_flag_media_projection ) for the device
        handle_app_start &

        # For async launch flow, wait for download and install to complete before launching the app
        if should_use_async_app_launch_setup; then
          # Start a subshell that will wait for the signal and then launch the user app
          (
              launch_wait_start_time=$(date +%s)
              read -t $ASYNC_APP_LAUNCH_PIPE_TIMEOUT -r < "$ASYNC_APP_LAUNCH_SYNC_PIPE_FILE"  # Wait for download & install to complete
              launch_wait_end_time=$(date +%s)
              launch_wait_time=$(( launch_wait_end_time - launch_wait_start_time ))
              launch_app
              echo "Done with async launch (Waited for ${launch_wait_time}s)"
              rm "$ASYNC_APP_LAUNCH_SYNC_PIPE_FILE"         # Clean up the named pipe
          ) &
        fi
      fi
      mark_event_end "handle_app_start"

      enable_contacts_param=""
      if [ $ENABLE_CONTACTS_APP_ACCESS == true ]; then
        enable_contacts_param='--enable-contacts'
      fi
      echo "Enable contacts app: ${ENABLE_CONTACTS_APP_ACCESS}, ${enable_contacts_param}"

      mark_event_start "log_app_anr"
      log_app_anr &
      mark_event_end "log_app_anr"

      mark_event_start "touch_in_session_file"
      touch_in_session &
      mark_event_end "touch_in_session_file"

      mark_event_start "restart_check_status_bar"
      restart_check_status_bar "app_live" "${enable_contacts_param}" &
      mark_event_end "restart_check_status_bar"

      mark_event_start "push_privoxy_to_cls"
      push_privoxy_to_cls &
      mark_event_end "push_privoxy_to_cls"

      mark_event_start "log_gms_version"
      log_gms_version &
      mark_event_end "log_gms_version"

      mark_event_start "check_browserstack_apps"
      check_browserstack_apps &
      mark_event_end "check_browserstack_apps"

      mark_event_end "total_bash_start_time"

      log_bash_instrumentation "async_start_app"
      ;;
    install_app )
      SESSION_ID="$8"
      enable_apksigner="${9}"
      DEVICE_NAME="${10}"
      DEVICE_VERSION="${11}"
      USER_ID="${12}"
      IS_APP_AUTOMATE="${13}"
      download_path="${14}"
      force_resign="${16}"
      skip_resign="${17}"

      if [[ ${skip_resign} != "true" ]]; then
        start=$(($(date +%s%N)/1000000))
        if [[ ${enable_apksigner} == 'true' ]]; then
          sign_with_apksigner_and_zipalign
        else
          sign_and_zipalign "$7"
        fi
        end=$(($(date +%s%N)/1000000))
        timetaken=$((end-start))
        $BUNDLE exec $BS_DIR/mobile/common/push_to_zombie.rb "android" "app-signing-metrics" "$ZIP_ALIGN_STATUS" '' "$timetaken" "$DEVICE_NAME" "$SESSION_ID" "$USER_ID" "$DEVICE_VERSION" '' '' '' '' '' &
      fi

      install_app "$3" "$4" "$5" "$6" "" "${14}"
      ;;
    install_apks )
      SESSION_ID="$4"
      USER_ID="${5}"
      download_path="${6}"
      install_apks "$4" "$5" "$6"
      ;;
    install_instant_app )
      SESSION_ID="$4"
      USER_ID="${5}"
      download_path="${6}"
      install_instant_app "$4" "$5" "$6"
      ;;
    update_app )
      APP_DOWNLOAD_URL=$3
      APP_DOWNLOAD_TIMEOUT="${9}"
      APP_HASHED_ID=$4
      PREVIOUS_PACKAGE=$5
      launch_activity="${6}"
      package="${7}"
      relaunch_flow="false"
      switch_app="true"
      enable_apksigner="${8}"
      INSTRUMENTATION_TYPES="${10}"
      INSTRUMENTATION_PARAMS="${11}"
      IS_BIOMETRIC_TOGGLED="${12}"
      IS_CAMERA_TOGGLED="${13}"
      IS_WEBVIEW_TOGGLED="${14}"
      IS_NETWORK_LOGS_ENABLED="${15}"
      IS_SCREENSHOT_BLOCK_TOGGLED="${16}"
      IS_PASSCODE_TOGGLED="${17}"
      USERNAME="${18}"
      IS_NETWORK_LOGS_PATCH_TOGGLED="${19}"
      IS_CAMERA_PREVIEW_LAYER_TOGGLED="${20}"
      IS_VIDEO_TOGGLED="${21}"
      handle_app_start
      ;;
    stop_app )
      IS_APP_ACCESSIBILITY="${3}"
      if [ "$IS_APP_ACCESSIBILITY" == "true" ]; then
        TEAM_PRODUCT_STR=",team:app_a11y_dev,product:app_accessibility"
        EVENT_TYPE="web_events"
      else
        TEAM_PRODUCT_STR=""
        EVENT_TYPE="app_live_web_events"
      fi
      USE_RTC_APP=$(cat "$STATE_FILES_DIR/session_$DEVICEID" | jq ".use_rtc_app" 2>/dev/null | tr -d "\r\n\"")
      echo "Stopping app_live session version: $USE_RTC_APP"
      rtc_app_set_use_package $USE_RTC_APP
      stop_cpu_usage=$(cpuUsageForDevice $DEVICEID)
      stop_uptime=$(androidDeviceUptime $DEVICEID)
      stop_rtc_app_usage=$(cpuUsageForApp $DEVICEID "fr.pchab.AndroidRTC")
      session_id=$(cat /tmp/app_live_logs_params_$DEVICEID | cut -d " " -f2)
      echo "Stopping app_live session"
      ps aux | grep app_live_actions | grep start_app | grep $DEVICEID | awk '{print $2}' | sudo xargs kill -9
      rm -rf $STATE_FILES_DIR/session_$DEVICEID $CONFIG/rtc_service_$DEVICEID $APP_PATH $USERS_PATCHED_APP_PATH
      rm -f $SESSION_START_DIR/$DEVICEID
      stop_webrtc_app
      stop_inputinjector_and_screenshot "kill_all"
      stop_logcat_capture
      echo "Your device: $DEVICEID should be stopped version: $USE_RTC_APP"
      eds_data="stop_cpu_usage:${stop_cpu_usage},stop_uptime:${stop_uptime},stop_rtc_app_usage:${stop_rtc_app_usage},session_id:${session_id}$TEAM_PRODUCT_STR"
      $BS_DIR/mobile-common/common/eds.rb "/tmp/duplicate_session_$DEVICEID" $eds_data "$EVENT_TYPE" "false"
      ;;
    kill_all_user_apps )
      echo "Killing all user apps"
      counter=$(kill_all_user_apps)
      $BS_DIR/mobile-common/common/eds.rb "/tmp/duplicate_session_$DEVICEID" "kill_app_count:1" "app_live_test_sessions"
      ;;
    uninstall_all_user_apps )
      echo "Uninstalling all user apps"
      UNINSTALL_COUNTER=$(uninstall_all_user_apps)
      $BS_DIR/mobile-common/common/eds.rb "/tmp/duplicate_session_$DEVICEID" "uninstall_app_count:1" "app_live_test_sessions"
      ;;
    upload_app_live_logs )
      read tmp SESSION_ID AWS_KEY AWS_SECRET AWS_REGION AWS_BUCKET < <(cat /tmp/app_live_logs_params_$DEVICEID)

      upload_log_file "/tmp/app_log_${SESSION_ID}.log" "app_log.log"
      upload_log_file $DUP_APP_PROFILE_LOG "app_profiling.log"
      ;;
    upload_app_live_logs_forked )
      SESSION_ID=$3
      AWS_KEY=$4
      AWS_SECRET=$5
      AWS_REGION=$6
      AWS_BUCKET=$7

      upload_log_file "/tmp/app_log_${SESSION_ID}.log" "app_log.log"
      ;;
    full_reset_app )
      LOG_EVENTS="false"
      IS_APP_AUTOMATE="${7}"
      full_reset_app "$3" "$4" "$5" "$6"
      ;;
    stop_privoxy )
      ps -ax | grep 'privoxy\|privoxy_push' | grep $DEVICEID | awk '{print $1}' | sudo xargs kill -9
      ;;
    handle_app_launch_for_network_logs )
      APP_HASHED_ID=$2
      DEVICEID=$3
      launch_activity=$4
      package=$5
      relaunch_flow="true"
      IS_NETWORK_LOGS_ENABLED="true"
      APP_PATH="/tmp/app-${DEVICEID}.apk"
      USERS_PATCHED_APP_PATH="/tmp/app-${DEVICEID}-patched.apk"
      SESSION_ID=$6
      PROXY_PORT=$7
      PATCH_NEEDED=$8
      NETWORK_LOGS_2_0_ENABLED=$9
      FRIDA_APP_PATCHED_STATUS=${10}
      APP_DOWNLOAD_URL=${11}
      APP_DOWNLOAD_TIMEOUT=${12}
      APK_MITM_PATCH_STATUS=${13}
      enable_apksigner="true"

      if [ -z "$NETWORK_LOGS_2_0_ENABLED" ] || [ "$NETWORK_LOGS_2_0_ENABLED" != "true" ]; then
        echo "Network logs 2.0 is not enabled, using old network logs flow"
        handle_app_start
        exit 0
      fi
      echo "Network logs 2.0 is enabled, proceeding with new flow"
      if [ "$PATCH_NEEDED" == "true" ]; then
        if [ "$FRIDA_APP_PATCHED_STATUS" == "true" ]; then
          echo "Using Frida patched app"
          # Avoid using download url from /tmp/app_live_download_url_#{@device_id}
          INSTRUMENTATION_PARAMS="{}"
          IS_NETWORK_LOGS_PATCH_TOGGLED="true"
          relaunch_flow="false"
          # To avoid stuck in loop to wait apk mitm patch status.
          IS_NETWORK_LOGS_ENABLED="false"
          # If Frida patching was successful, uninstall, download, and start the app
          adb -s "$DEVICEID" uninstall "$package"
          download_app && start_app
        elif [ "$APK_MITM_PATCH_STATUS" == "true" ]; then
          # If APK MITM patching was successful, start the app normally
          echo "Using APK-MITM patched app"
          handle_app_start
        fi
      else
        # If patching is not needed, just restart the app
        kill_and_launch_app
      fi

esac
