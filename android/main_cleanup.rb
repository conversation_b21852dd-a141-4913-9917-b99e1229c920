require_relative '../common/helpers'
require_relative '../common/push_to_zombie'
require_relative 'constants'
require_relative 'helpers/battery_helper'
require_relative 'helpers/enable_location'
require_relative 'helpers/network_simulator'
require_relative 'helpers/performance_statistics'
require_relative 'helpers/rtcapp_release_helper'
require_relative 'helpers/local_testing_chrome_extension_helper'
require_relative 'helpers/settings_helper'
require_relative 'helpers/state_file_helper'
require_relative 'helpers/uiautomation_helper'
require_relative 'helpers/cleanup_helper'
require_relative 'helpers/cleanup_function_runner'
require_relative 'lib/device_logger_metric'
require_relative 'lib/root_command'
require_relative 'models/android_device'
require_relative 'scripts/upload_to_s3'
require_relative 'version_managers/device_owner_manager'
require_relative 'version_managers/browserstack_app_manager'
require_relative 'version_managers/play_services_manager'
require_relative './helpers/popup_helper'
require_relative './helpers/device_passcode'
require_relative './helpers/systemd_helper'
require_relative 'lib/battery_metrics'
require_relative './helpers/custom_certificate_helper'
require_relative 'exit_file'

require '/usr/local/.browserstack/mobile-common/utils/time_recorder'
require 'android_toolkit'
require 'browserstack_utils'
require 'date'
require 'fileutils'
require 'json'
require 'logger'
require 'csv'

# This MainCleanup class is created to move the necessary steps which needs to be run during cleanup of device.

class MainCleanup  # rubocop:todo Metrics/ClassLength
  include BrowserStack
  include TimeRecorder
  include SystemDHelper

  methods_to_wrap = %i[
    disable_always_finish_activities
    log_sim_messages
    enable_animations
    disconnect_call
    remove_sim_popup
    stop_performance_statistics_service
    enable_play_store
    clean_network_simulation
    stop_mocking_properties
    log_check_statusbar
    log_android_opened_apps
    stop_device_logger_monitoring
    handle_security_alerts
    stop_mock_location_service
    disable_samsung_game_tools
    check_outside_browser
    handle_samsung_recommendation_app_popup
    ensure_essential_binaries
    kill_device_binaries
    reduce_volume
    reset_custom_props
    push_user_installed_applist_to_cls
    handle_reboot_in_cleanup
    check_battery
    local_testing_chrome_extension_cleanup
    check_scrcpy_detected
    grant_write_settings_bs_app
    whitelist_device_owner_from_battery_optmisation
    disable_telephony_alert_samsung
    reset_foldable_screen
    disable_screen_share_protections
    clean_session_video_files
    remove_custom_certificates_for_applive
    remove_custom_certificate
    remove_voice_input_method_service
    clear_setup_wizard
    remove_all_custom_ca_certificates
  ]

  # around_method takes a list of method names, and a block to be executed which wraps around the original method,
  # see mobile-common/utils/method_interceptor.rb
  around_method(*methods_to_wrap) do |original_method, method_name|
    record_time(method_name, device_id, "android") { original_method.call }
  end

  attr_reader :device_id, :session_id

  def initialize(device_id, session_id, logger, last_session_type)
    @device_id = device_id
    @session_id = session_id
    @logger = logger
    @logger_params = {
      component: 'MainCleanup',
      session: @session_id,
      device: @device_id
    }

    @adb = AndroidToolkit::ADB.new(udid: @device_id, path: ADB)
    AndroidToolkit::Log.logger = @logger

    @device_model = @adb.model
    @device_manufacturer = @adb.manufacturer
    @device_type = @adb.characteristics
    @os_version = @adb.os_version.to_s
    @last_session_type = last_session_type
    @session_type = session_type
    @device_json = JSON.parse(read_with_lock(CONFIG_JSON_FILE))['devices'][@device_id]
    @device_name = @device_json['device_name']
    @device_version = @device_json['device_version']
    @device_port = @device_json['port']
    @static_conf = begin
      JSON.parse(File.read('/usr/local/.browserstack/config/static_conf.json'))
    rescue StandardError
      {}
    end
    @cleanup_function_runner = CleanupFunctionRunner.new(@device_id, @logger)
    @influxdb_client = AndroidInfluxdbClient.new(@logger, @logger_params)
  end

  def device_obj
    @device_obj ||= AndroidDevice.new(@device_id, self.class.to_s, @logger, @logger_params)
  end

  def settings_helper
    @settings_helper ||= SettingsHelper.new(@device_id, @logger)
  end

  def popup_helper
    @popup_helper ||= BrowserStack::PopupHelper.new(device_id: @device_id, os_version: Gem::Version.new(@os_version),
                                                    session_id: @session_id)
  end

  def ui_automation_helper
    @ui_automation_helper ||= UiAutomationHelper.new(@device_id, @logger, @logger_params)
  end

  def root_command
    @root_command ||= RootCommand.new(device_obj, @logger, @logger_params)
  end

  def enable_location
    @enable_location ||= EnableLocation.new(device_obj, @logger, @logger_params)
  end

  def browserstack_app_manager
    @browserstack_app_manager ||= BrowserStackAppManager.new(@device_id, @logger, @logger_params)
  end

  def device_owner_manager
    @device_owner_manager ||= DeviceOwnerManager.new(@device_id, @logger, @logger_params)
  end

  def device_passcode_manager
    @device_passcode_manager ||= DevicePasscodeManager.new(@device_id, @logger, @session_id)
  end

  def play_services_manager
    @play_services_manager ||= PlayServicesManager.new(@device_id, @logger, @logger_params)
  end

  def cleanup_function_runner
    @cleanup_function_runner ||= CleanupFunctionRunner.new(@device_id, @logger)
  end

  def reset_foldable_screen
    return unless device_obj.foldable_device?

    log :info, "Resetting foldable device screen state", cleanup_flow: true
    @adb.shell('cmd device_state state 0')
  end

  def ensure_privoxy_service_stopped
    systemd_stop_service("privoxy", device: @device_id) if systemd_check_if_service_in_list("privoxy", device: @device_id) # rubocop:disable Layout/LineLength
  end

  def ensure_adb_forwarder_service_stopped
    if systemd_check_if_service_in_list(ADB_FORWARDER, device: @device_id)
      systemd_stop_service(ADB_FORWARDER, device: @device_id)

      eds = EDS.new({}, @logger)
      eds_data = {
        event_name: 'ADB_FORWARDER_SERVICE_CLEANUP',
        genre: 'app_live_testing',
        device_id: @device_id,
        session_id: @session_id,
        product: APP_LIVE
      }
      eds.push_logs(EdsConstants::APP_LIVE_WEB_EVENTS, eds_data)
    end
  end

  def remove_custom_certificate
    custom_certificate_installed_file = StateFileHelper.new("custom_certificate_installed_#{@device_id}")
    if custom_certificate_installed_file.exist?
      CustomCertificate.new(@device_id, @session_id, '',
                            @logger).remove_pfx_certificate
    end
  end

  def remove_all_custom_ca_certificates
    custom_certificate_installed_file = StateFileHelper.new("custom_ca_certificate_installed_#{@device_id}")
    if custom_certificate_installed_file.exist?
      CustomCertificate.new(@device_id, @session_id, '',
                            @logger).remove_all_ca_cert
    end
  rescue StandardError => e
    log(:error, "Error while removing custom_ca_certificates: #{e.message}")
    zombie_key_value(
      platform: 'android',
      kind: 'android_remove_all_ca_certificates_failed',
      error: e.message,
      device: @device_id,
      os_version: @os_version.to_s,
      session: @session_id
    )
    # Raising the exception here will cause cleanup to fail which is expected
    raise e
  end

  def remove_custom_certificates_for_applive
    if File.exist?("#{STATE_FILES_DIR}/app_live_custom_certificates_#{@device_id}")
      CustomCertificate.new(@device_id, @session_id, '',
                            @logger).remove_pfx_certificate_for_applive
    end
  end

  def disable_always_finish_activities
    state_file_helper = StateFileHelper.new("always_finish_activities_#{@session_id}")
    if state_file_helper.exist?
      package_path = @adb.shell("pm path com.android.browserstack").split(":")[1].chomp
      method_name = "alwaysFinishActivities"
      @adb.shell(
        "CLASSPATH=#{package_path} app_process / com.android.browserstack.Main #{method_name} false"
      )
      state_file_helper.remove
    end
  rescue StandardError => e
    log(:error, "Error while disabling always finish activities: #{e.message}")
    zombie_key_value(
      platform: 'android',
      kind: 'android_disable_always_finish_activities_failed',
      error: e.message,
      device: @device_id,
      os_version: @os_version.to_s,
      session: @session_id
    )
    # Raising the exception here will cause cleanup to fail which is expected
    raise e
  end

  def log_sim_messages
    is_sim_sess = !@adb.shell("ls /sdcard/public_sim_session").strip.empty?
    if is_sim_sess
      val = @adb.shell("content query --uri content://sms --projection address,body")
      unless val.chomp.eql?("No result found.")
        zombie_key_value(
          platform: 'android',
          kind: 'android_sms_logs',
          data: val,
          device: @device_id,
          os_version: @os_version.to_s,
          session: @session_id
        )
      end
    end
  rescue StandardError => e
    log(:error, "Error while loggign sim messages: #{e.message}")
    zombie_key_value(
      platform: 'android',
      kind: 'android_sms_logs_failed',
      error: e.message,
      device: @device_id,
      os_version: @os_version.to_s,
      session: @session_id
    )
  end

  def log_android_opened_apps
    filename = "/tmp/android_launched_apps/#{@session_id}"
    if File.exist?(filename)
      apps_opened = File.read(filename).to_s
      zombie_key_value(
        platform: 'android',
        kind: 'all_apps_opened',
        data: apps_opened,
        device: @device_id,
        os_version: @os_version.to_s,
        session: @session_id
      )
    end
  rescue StandardError => e
    log(:error, "Error in log_android_opened_apps: #{e.message}")
    zombie_key_value(
      platform: 'android',
      kind: 'android_apps_opened_failed',
      error: e.message,
      device: @device_id,
      os_version: @os_version.to_s,
      session: @session_id
    )
  # We need this file later in cleanup
  # ensure
  #   FileUtils.rm_f(filename)
  end

  def fix_playstore_permissions
    # Permissions in playstore are fixed on reinstallation of Google Play services
    # Incorrect permissions in Google Play Store causes play store to crash
    play_services_reinstall_required_file = "#{STATE_FILES_DIR}/#{PLAY_SERVICES_REINSTALL_REQUIRED}_#{@device_id}"
    return unless File.exist?(play_services_reinstall_required_file)

    log(:info, "Reinstalling play services to fix play store crash due to permission issue: #{@device_id}")
    BrowserStackUtils::LogCleanupFlow.log(@device_id, Time.now.to_i, "Reinstalling google play services")
    play_services_manager.reinstall
    log(:info, "Google Play services successfully reinstalled: #{@device_id}")
    BrowserStackUtils::LogCleanupFlow.log(@device_id, Time.now.to_i, "Disabling google play store auto updates")
    ui_automation_helper.handle_playstore_updates(@os_version)
    FileUtils.rm(play_services_reinstall_required_file)
    zombie_key_value(
      platform: 'android',
      kind: 'play-services-reinstall-success',
      error: "Google Play services successfully reinstalled",
      device: @device_id,
      os_version: @os_version.to_s,
      session: @session_id
    )
  rescue StandardError => e
    log(:error, "Google Play services reinstallation failed: #{e.message}")
    zombie_key_value(
      platform: 'android',
      kind: 'play-services-reinstall-failed',
      error: e.message,
      device: @device_id,
      os_version: @os_version.to_s,
      session: @session_id
    )
  end

  def optimised_run
    enable_animations
    disconnect_call
    remove_sim_popup
    stop_performance_statistics_service
    enable_play_store
    run
  end

  def run
    disable_always_finish_activities
    log_sim_messages
    log_check_statusbar
    ensure_essential_binaries
    kill_device_binaries
    reduce_volume
    reset_custom_props
    check_battery
    local_testing_chrome_extension_cleanup
    # push_user_installed_applist_to_cls
    handle_reboot_in_cleanup
    check_scrcpy_detected
    clean_network_simulation
    stop_mocking_properties
    esim_check
    stop_device_logger_monitoring
    check_outside_browser
    handle_samsung_recommendation_app_popup
    grant_write_settings_bs_app
    whitelist_device_owner_from_battery_optmisation
    ensure_privoxy_service_stopped
    ensure_adb_forwarder_service_stopped
    reset_foldable_screen
    reset_wallpaper
    infer_battery_metrics
    remove_observability_sdk_files
    clean_session_video_files
    remove_all_custom_ca_certificates
    remove_custom_certificates_for_applive
    remove_custom_certificate
    log_android_opened_apps
    clean_app_a11y_session_files
    clean_watcher_google_block_file
    set_verbose_logging if cleanup_function_runner.should_run?("set_verbose_logging")
    enable_button_navbar if cleanup_function_runner.should_run?("enable_button_navbar")
  end

  def can_generate_battery_metrics
    @os_version.to_i > 10 && device_obj.supports_device_owner?
  end

  def touch_battery_metrics_file
    if can_generate_battery_metrics
      log :info, "Touching /sdcard/need_battery_metrics for android: #{@os_version.to_i}"
      @adb.shell("touch /sdcard/need_battery_metrics")
    else
      log :info, "Not touching /sdcard/need_battery_metrics for android: #{@os_version.to_i}"
    end
  end

  def infer_battery_metrics
    if can_generate_battery_metrics
      log :info, "Infering metrics for android #{@os_version.to_i}"
      battery_metrics = BatteryMetrics.new(@device_id, @session_id, @last_session_type, @logger, @logger_params)
      battery_metrics.generate_and_push_battery_inference
    else
      log :info, "Not infering metrics for android: #{@os_version.to_i}"
    end
  end

  def reset_wallpaper
    activity_name = "com.android.browserstack/com.android.browserstack.main.CleanupActivity"
    # For android v<=7 and CPH2035,
    # We call the activity with the older action as we cant update the app due to gradle build issues
    action_name = @os_version.to_i <= 7 || @device_model == 'CPH2035' ? "restore_settings" : "resetWall"
    @adb.shell("am start -n #{activity_name} --es action #{action_name}")
  end

  def set_verbose_logging
    @adb.shell("setprop persist.log.tag V")
  end

  def enable_button_navbar

    gestural_package = "com.android.internal.systemui.navbar.gestural"
    button_package = "com.android.internal.systemui.navbar.threebutton"

    @adb.shell("cmd overlay disable #{gestural_package}")
    # Disabling the 3-button navigation overlay (Samsung devices) to fix button navigation issue
    @adb.shell("cmd overlay disable #{button_package}")

    nav_mode = @adb.shell("settings get secure navigation_mode").strip

    if nav_mode == "0"
      log :info, '"Enable button navigation" setting is enabled'
    else
      log :warn, "Button Navigation setting is not enabled (navigation_mode=#{nav_mode})"
    end
  rescue StandardError => e
    log :error, "Error while enabling button navigation: #{e.message}"
  end

  def grant_write_settings_bs_app
    browserstack_app_manager.grant_bs_write_settings_via_appops
  rescue StandardError => e
    log :info, "Unable to grant bs apk write settings permission: #{e.message}"
    zombie_key_value(
      platform: 'android',
      kind: 'bs_apk_write_settings_failure_appops',
      data: @last_session_type,
      device: @device_id,
      os_version: @os_version.to_s,
      session: @session_id
    )
  end

  def whitelist_device_owner_from_battery_optmisation
    return unless device_obj.supports_device_owner?

    device_owner_manager.whitelist_from_battery_optmisation
  end

  def disable_mobile_data
    return unless device_obj.samsung? || @device_model.include?("Pixel")

    log :info, "Disabling mobile data"
    @adb.shell("settings put global mobile_data_always_on 0")
    @adb.shell("settings put global mobile_data 0")
    @adb.shell("settings put global mobile_data1 0") # For dual sim devices
  rescue StandardError => e
    log :error, "Error disabling mobile data - #{e.message}"
    zombie_key_value(
      platform: 'android',
      kind: "disable-mobile-data-failure",
      data: @device_model,
      error: e.message,
      device: @device_id,
      os_version: @os_version.to_s,
      session: @session_id
    )
  end

  def esim_check
    log :info, "Checking if esim is present"
    BrowserStackUtils::LogCleanupFlow.log(@device_id, Time.now.to_i, "Removing esims")

    esim_states = @adb.shell("getprop gsm.sim.state")
    return unless esim_states.include?("LOADED")

    # Checking if physical sim or ESIM
    begin
      esim_file_path = "/sdcard/esim.prop"
      @adb.am(
        "startservice --user 0 -n com.android.browserstack/.services.TelephonyHandlerService "\
        "--es action \"check-if-esim\""
      )
      @adb.shell("cat #{esim_file_path}")
    rescue StandardError => e
      log :info, "Couldn't find esim file, not running esim cleanup"
      return
    end

    log :info, "Esim present, pushing to BQ"
    @adb.shell("rm #{esim_file_path}")

    zombie_key_value(
      platform: 'android',
      kind: "esim-present",
      data: @device_model,
      device: @device_id,
      os_version: @os_version.to_s,
      session: @session_id
    )

    return unless device_obj.remove_esims?

    @adb.shell("settings put system screen_off_timeout 86400000")
    @adb.shell("input keyevent KEYCODE_WAKEUP")

    # for samsung v11 alone we need to set the passcode to remove esim
    device_passcode_manager.set_passcode if @os_version.to_s == "11"

    log :info, "Runnng automation for eSim removal"
    begin
      ui_automation_helper.remove_esims
    rescue StandardError => e
      log :error, "Failed automation, retrying"
      begin
        ui_automation_helper.remove_esims
      rescue StandardError => e
        log :error, "Failed retry"
        log :error, "eSim present after automation, pushing to BQ: #{e.message}"
        zombie_key_value(
          platform: 'android',
          kind: "esim-automation-failure",
          error: e.message,
          data: @device_model,
          device: @device_id,
          os_version: @os_version.to_s,
          session: @session_id
        )
      else
        log :info, "eSim automation success after retry"
      end
    end
    device_passcode_manager.clear_passcode if @os_version.to_s == "11"
  end

  def enable_animations
    BrowserStackUtils::LogCleanupFlow.log(@device_id, Time.now.to_i, "Enabling animations")
    @adb.shell("settings put global window_animation_scale 1")
    @adb.shell("settings put global transition_animation_scale 1")
    @adb.shell("settings put global animator_duration_scale 1")
  end

  def disconnect_call
    BrowserStackUtils::LogCleanupFlow.log(@device_id, Time.now.to_i, "Disconnecting call")
    adb_command = "dumpsys telephony.registry | grep -w mCallState=0"
    call_disconnected = @adb.shell(adb_command)
    count = 0
    while count < 10 && !call_disconnected
      log :info, "trying to disconnect call.. count: #{count}"
      @adb.shell("input keyevent KEYCODE_ENDCALL")
      call_disconnected = @adb.shell(adb_command)
      count += 1
    end
  end

  def remove_sim_popup
    BrowserStackUtils::LogCleanupFlow.log(@device_id, Time.now.to_i, "remove sim-card popups (if any)")
    begin
      phone_popup_present = @adb.shell("dumpsys window | grep mCurrentFocus | grep -w com.android.phone")
    rescue AndroidToolkit::ADB::ExecutionError => e
      log :error, "Error in executing adb command: #{e.message}"
      phone_popup_present = nil
    end

    if phone_popup_present
      pid = @adb.shell("ps | grep com.android.phone").split(" ")[1].tr('\r\n', '')
      root_command.run("kill #{pid}") if pid
    end
  end

  def stop_performance_statistics_service
    BrowserStackUtils::LogCleanupFlow.log(@device_id, Time.now.to_i, "Stopping performance statistics service")

    begin
      @adb.shell(
        "#{BUSYBOX} ps aux "\
        "| grep performance_statistics.sh "\
        "| #{BUSYBOX} awk '{print \$1}' "\
        "| #{BUSYBOX} xargs kill -9"
      )
    rescue AndroidToolkit::ADB::ExecutionError => e
      log :error, "Error in executing adb command: #{e.message}"
    end

    begin
      perform_stats_helper = PerformanceStatistics.new(@device_id, @logger)
      perform_stats_helper.send("pull_performance_stats_logs".to_sym)
    # pull_performance_stats_logs is a private method of PerformanceStatistics class
    rescue AndroidToolkit::ADB::ExecutionError, Errno::EPERM => e
      log :error, "Error in executing cleanup for performance stats logs: #{e.message}"
    end

    upload_url = mobile_session_get("s3.stats.url").gsub("\n", "")
    s3_id = mobile_session_get("s3.stats.id").gsub("\n", "")
    s3_key = mobile_session_get("s3.stats.key").gsub("\n", "")
    s3_storage_class = mobile_session_get("s3.stats.storage_class").gsub("\n", "")
    s3_region = if upload_url
                  upload_url.gsub(/.amazonaws.*/, '').gsub("https://s3-", '')
                else
                  ""
                end

    csv_file = "/tmp/#{@last_session_type}_#{@session_id}.csv"
    parse_csv_for_system_data(csv_file)

    if File.exist?(
      "/usr/local/.browserstack/files_to_be_processed/files_to_upload/async_#{@session_id}"
    )
      file_name = "/var/log/browserstack/app_profiling_#{@device_id}_#{@session_id}.csv"
      FileUtils.cp(csv_file, file_name)
      json_data = {
        upload_type: "profile-file",
        file_name: file_name,
        session_id: @session_id,
        upload_url: upload_url,
        s3_id: s3_id,
        s3_key: s3_key,
        s3_region: s3_region,
        session_type: session_type
      }.to_json
      File.write(
        "/usr/local/.browserstack/files_to_be_processed/files_to_upload/OTHER/profile_file_#{@session_id}.json",
        json_data
      )
      log :info, "removing performance statistics csv file"
      File.delete(csv_file) if File.exist?(csv_file)
    else
      upload_performance_statistics_logs(
        csv_file, s3_id, s3_key, "text/csv", "public-read", upload_url, s3_region, 900, s3_storage_class
      )
    end

    package_list = "/tmp/#{@device_id}_package_list_profiling.txt"
    File.delete(package_list) if File.exist?(package_list)
  end

  def parse_csv_for_system_data(csv_data)

    session_params = begin
      JSON.parse(File.read(DUPLICATE_SESSION_FILE.gsub('device_id', @device_id)), symbolize_names: true)
    rescue StandardError
      {}
    end
    return unless ['espresso', 'fluttertest'].include?(session_params[:browserstack_framework])

    app_profiling_stats_data = File.read(csv_data)
    app_profiling_data_csv = CSV.new(app_profiling_stats_data, headers: true, header_converters: :symbol,
                                                               converters: :all)
    app_profiling_data_csv_hash = []
    app_profiling_data_csv.each do |row|
      hash = row.to_hash
      app_profiling_data_csv_hash.push(hash)
    end
    min_cpu_utilization, max_cpu_utilization, average_cpu_utilization, min_ram_utilization, max_ram_utilization,
    average_ram_utilization = fetch_app_profiling_data(app_profiling_data_csv_hash)
    data_to_push = {
      "min_cpu_utilization": min_cpu_utilization,
      "max_cpu_utilization": max_cpu_utilization,
      "average_cpu_utilization": average_cpu_utilization,
      "min_ram_utilization": min_ram_utilization,
      "max_ram_utilization": max_ram_utilization,
      "average_ram_utilization": average_ram_utilization
    }
    zombie_key_value(
      platform: 'android',
      kind: 'app-profiling-stats',
      data: data_to_push,
      device: @device_id,
      os_version: @os_version.to_s,
      session: @session_id
    )
  rescue StandardError => e
    log(:error, "Error while parsing app profiling data: #{e.message}")
    zombie_key_value(
      platform: 'android',
      kind: 'app-profiling-csv-parse-failed',
      error: e.message,
      device: @device_id,
      os_version: @os_version.to_s,
      session: @session_id
    )

  end

  def fetch_app_profiling_data(objects)
    min_cpu = nil
    max_cpu = nil
    total_cpu = 0
    min_ram_utilized = nil
    max_ram_utilized = nil
    total_ram_utilized_percentage = 0

    objects.each do |obj|
      system_cpu = obj[:cpu]
      min_cpu = system_cpu if min_cpu.nil? || system_cpu < min_cpu
      max_cpu = system_cpu if max_cpu.nil? || system_cpu > max_cpu
      total_cpu += system_cpu

      system_ram_utilized = obj[:mem] - obj[:mema]
      system_ram_utilized_percentage = system_ram_utilized * 100 / obj[:mem]
      if min_ram_utilized.nil? || system_ram_utilized_percentage < min_ram_utilized
        min_ram_utilized = system_ram_utilized_percentage
      end
      if max_ram_utilized.nil? || system_ram_utilized_percentage > max_ram_utilized
        max_ram_utilized = system_ram_utilized_percentage
      end
      total_ram_utilized_percentage += system_ram_utilized_percentage
    end

    average_cpu = total_cpu.to_f / objects.length
    average_ram_utilized = total_ram_utilized_percentage.to_f / objects.length

    [min_cpu, max_cpu, average_cpu, min_ram_utilized, max_ram_utilized, average_ram_utilized]
  end

  def current_session_id
    mobile_session_get('session_id').chomp
  end

  def upload_performance_statistics_logs(
    csv_file, s3_id, s3_key, file_format, file_acl, upload_url, s3_region, timeout, s3_storage_class = "STANDARD"
  )
    log :info, "upload csv file #{current_session_id} to s3"
    file_size = if File.exist?(csv_file)
                  File.size(csv_file) / 1024.0
                else
                  0
                end
    s3_storage_class ||= "STANDARD"
    s3_storage_class = "STANDARD" if s3_storage_class && s3_storage_class.to_s.empty?
    s3_storage_class = "STANDARD" if s3_storage_class && s3_storage_class != "STANDARD" && file_size && file_size < 128
    eds_kind = case @last_session_type
               when "app_automate"
                 EdsConstants::APP_AUTOMATE_TEST_SESSIONS
               when "live_testing"
                 EdsConstants::LIVE_TEST_SESSIONS
               when "app_live_testing"
                 EdsConstants::APP_LIVE_TEST_SESSIONS
               when "automate"
                 EdsConstants::AUTOMATE_TEST_SESSIONS
               else
                 "undefined_eds_session_type"
               end

    status, error = UploadToS3.upload_file_to_s3(
      s3_id, s3_key, file_format, csv_file, file_acl, upload_url, s3_region, timeout, s3_storage_class
    )

    eds_data = if error
                 {
                   "feature_usage": {
                     "profiling": {
                       "success": false.to_s,
                       "size": file_size.to_s,
                       "storage_class": s3_storage_class,
                       "exception": error.to_s
                     }
                   },
                   "hashed_id": current_session_id,
                   "timestamp": Time.now.to_i.to_s
                 }
               else
                 {
                   "feature_usage": {
                     "profiling": {
                       "success": true.to_s,
                       "size": file_size.to_s,
                       "storage_class": s3_storage_class
                     }
                   },
                   "hashed_id": current_session_id,
                   "timestamp": Time.now.to_i.to_s
                 }
               end

    eds = EDS.new({}, @logger)
    eds.push_logs(eds_kind, eds_data)
    log :info, "removing performance statistics csv file"
    File.delete(csv_file) if File.exist?(csv_file)
  end

  def enable_play_store
    BrowserStackUtils::LogCleanupFlow.log(@device_id, Time.now.to_i, "Enabling play store")
    return if device_obj.browser_only_device? || device_obj.exclude_and_uninstall_google_apps

    if device_obj.rooted?
      root_command.run("pm enable #{PLAY_STORE_PACKAGE_NAME}")
    else
      @adb.pm("enable #{PLAY_STORE_PACKAGE_NAME}")
    end
  rescue StandardError => e
    log(:error, e.message)
    CleanupHelper.record_cleanup_failure_reason(@device_id, "Unable to enable play store: #{e.message}")
    exit 200
  end

  def disable_airplane_mode
    log :info, "Disabling airplane mode"
    if device_obj.rooted?
      root_command.run(
        "settings put global airplane_mode_on 0; "\
        "am broadcast -a android.intent.action.AIRPLANE_MODE"
      )
    elsif device_obj.supports_device_owner?
      device_owner_manager.turn_off_airplane_mode
    elsif device_obj.airplane_mode_enabled?
      log :info, "Can't disable airplane mode on this device"
      CleanupHelper.record_cleanup_failure_reason(@device_id, "manual fix: Airplane mode enabled")
      exit 255
    end
  end

  def ensure_network_services
    log :info, "Ensuring network services"
    disable_airplane_mode
    if device_obj.rooted?
      root_command.run("pm enable #{BROWSERSTACK_APP_PACKAGE_NAME}/.receiver.WifiBroadcastReceiver")
    elsif @device_model == "BON-AL00"
      @adb.shell("svc wifi enable")
    else
      begin
        @adb.am(
          "startservice --user 0 -n #{BROWSERSTACK_APP_PACKAGE_NAME}/.services.WifiHandlerService "\
          "--es action \"enable\""
        )
      rescue StandardError => e
        log :error, "Error enabling wifi: #{e.message}, trying to start in foreground"
        @adb.am(
          "start-foreground-service --user 0 -n #{BROWSERSTACK_APP_PACKAGE_NAME}/.services.WifiHandlerService "\
          "--es action \"enable\""
        )
      end
    end
  end

  def start_services
    log :info, "Starting services"
    start_services_code = 0
    @adb.am("force-stop #{BROWSERSTACK_APP_PACKAGE_NAME}")
    @adb.am(
      "start -n \"#{BROWSERSTACK_APP_PACKAGE_NAME}/com.android.browserstack.main.BrowserStack\" "\
      "--es setWifi \"true\" --es ssid \"#{@static_conf['ssid']}\" --es pass \"#{@static_conf['wifi_password']}\""
    )
    begin
      start_command = 'startservice --user 0 -n fr.pchab.AndroidRTC/.RTCService --es action \"foo\"'
      @adb.am(start_command) unless device_obj.uses_64bit_rtc_app?
    rescue AndroidToolkit::ADB::ExecutionError => e
      popup_helper.run_main_automation(exit_on_failure: false) if e.message.include? "no service started"
    end

    sleep(2)
    res = `adb -s #{@device_id} shell 'ps' | grep -c RTC`
    start_services_code = 1 if res.to_i < 1
    log :info, "[#{@device_id}] All services have been restarted"
    start_services_code
  end

  def clean_network_simulation
    BrowserStackUtils::LogCleanupFlow.log(@device_id, Time.now.to_i, "Cleaning network simulation")
    return unless File.exist?("/tmp/network_simulation_#{@device_id}")

    # Need to remove this before ensure_network_services,
    # otherwise BrowserStack app wont enable wifi
    @adb.shell("rm -f /sdcard/disable_wifi")

    network_simulator = NetworkSimulator.new(@device_id, @device_port, "cleanup")
    network_simulator.reset_simulation
    remove_file("/tmp/internet_via_usb_#{@device_id}")
    ensure_network_services
    start_services
  end

  def stop_mocking_properties
    BrowserStackUtils::LogCleanupFlow.log(@device_id, Time.now.to_i, "Stop mocking properties")
    battery_helper = BatteryHelper.new(@device_id, @os_version, @logger)
    battery_helper.stop_mocking_properties
  end

  def update_location_package_blacklist
    return unless ['SM-G965F', 'SM-G960F'].include?(@device_model)

    @adb.shell('settings put secure locationPackagePrefixBlacklist com.semaphoremobile.zagat.android')
  end

  def enable_google_location_services
    return unless ["SM-N900", "SM-N9005"].include?(@device_model)

    # Updates use_location_for_services property to 1
    root_command.run(
      "content insert"\
      " --uri content://com.google.settings/partner"\
      " --bind name:s:use_location_for_services --bind value:i:1"
    )

    @adb.shell("am broadcast -a com.google.android.gsf.settings.GoogleLocationSettings.UPDATE_LOCATION_SETTINGS")
  end

  def restart_sensors
    @adb.shell("service call sensor_privacy 8 i32 1") # Disable sensors
    @adb.shell("input keyevent 26") # Lock/unlock device
    @adb.shell("service call sensor_privacy 8 i32 0") # Enable sensors again
    @adb.shell("input keyevent 26") # Bring device back to original state i.e locked/unlocked
  end

  def google_camera_popup_handler
    gcam_bundle_id = "com.google.android.GoogleCamera"
    gcam_launcher = "#{gcam_bundle_id}/com.android.camera.CameraLauncher"

    permissions = [
      "android.permission.ACCESS_FINE_LOCATION",
      "android.permission.ACCESS_COARSE_LOCATION"
    ]
    @adb.pm_clear(gcam_bundle_id)
    @adb.grant_permission(gcam_bundle_id, permissions)

    @adb.am("start -n #{gcam_launcher}")
    sleep 2 # wait for popups to load

    adb_keyevent(20, 3) # simulate down key 3 times
    adb_keyevent(23, 1) # simulate enter key once

    @adb.am("force-stop #{gcam_bundle_id}")
    sleep 1
    @adb.am("start -n #{gcam_launcher}")
    # wait for popup to load
    # just launching the app is enough to make sure it does not come up again
    sleep 3
    @adb.am("force-stop #{gcam_bundle_id}")
  rescue StandardError => e
    log :error, "Unable to handle GCam popups: #{e.message}"
    zombie_key_value(
      platform: 'android',
      kind: 'gcam_popup_handler_failed',
      data: @last_session_type,
      device: @device_id,
      os_version: @os_version.to_s,
      session: @session_id
    )
  ensure
    @adb.shell("input keyevent KEYCODE_HOME")
  end

  def adb_keyevent(key_event, times)
    times.times do
      @adb.shell("input keyevent #{key_event}")
      sleep 1
    end
  end

  def disable_galaxy_ai
    @adb.shell("pm clear com.sec.android.app.launcher") # clears launcher app , removes greyed apps
    # Commands to toggle off settings
    packages_list = @adb.list_packages
    @adb.shell("settings put secure assist_touch_gesture_enabled 0")
    @adb.shell("settings put secure content_capture_enabled 0")
    @adb.shell("settings put global galaxy_system_update 0")
    @adb.shell("settings put secure touch_and_hold_to_search 0")
    # Commands to disable apps that appear greyed out
    packages_to_disable = ["com.sec.android.app.clockpackage",
                           "com.samsung.android.game.gamehome",
                           "com.samsung.android.app.notes",
                           "com.samsung.android.app.tips",
                           "com.samsung.android.calendar",
                           "com.samsung.android.app.spage",
                           "com.sec.android.app.shealth",
                           "com.samsung.android.oneconnect",
                           "com.samsung.android.voc",
                           "com.samsung.sree",
                           "com.samsung.android.game.gamehome"]

    packages_to_disable.each do |package|
      disable_package_if_exists(package, packages_list)
    end
  end

  def disable_package_if_exists(package_name, packages_list)
    @adb.shell("pm disable-user #{package_name}") if packages_list.include?(package_name)
  end

  def needs_full_cleanup
    needs_full_cleanup_file = File.join(STATE_FILES_DIR, "needs_full_cleanup_#{device_id}")
    case @device_model
    when 'SM-G973F'
      result_count = @adb.shell("dumpsys window | grep -E 'mCurrentFocus|mFocusedApp' | grep 'null' -c")
      if result_count > 0
        log :info, "Dark Screen Detected, Cleanup converted to full cleanup", cleanup_flow: true
        FileUtils.touch(needs_full_cleanup_file)
      end
    when 'Pixel 6 Pro'
      if @os_version.to_i == 15
        counter_file = File.join(STATE_FILES_DIR, "pixel_cleanup_count_#{device_id}")
        counter_value = if File.exist?(counter_file)
                          File.read(counter_file).to_i
                        else
                          FileUtils.touch(counter_file)
                          File.write(counter_file, "0")
                        end
        if counter_value >= 5
          File.write(counter_file, "0")
          log :info, "Moving to Full Cleanup considering 5th Cleanup counter_val- #{counter_value}", cleanup_flow: true
          FileUtils.touch(needs_full_cleanup_file)
        else
          counter_value += 1
          File.write(counter_file, counter_value.to_s)
          0
        end
      end
    end
  end

  def remove_circle_to_search_popup
    @adb.shell("input keyevent KEYCODE_BACK")
  end

  def set_screen_density450
    @adb.shell('wm density 450')
  end

  def device_model_based_updates
    if @cleanup_function_runner.should_run?("immersive_mode_confirmations")
      settings_helper.immersive_mode_confirmations("3")
    end

    if @cleanup_function_runner.should_run?("disable_setup_wizard_motorola")
      # Disable setup wizard for Moto G71 to avoid setup popups
      @adb.shell('pm disable-user com.motorola.setup')
    end

    if @cleanup_function_runner.should_run?("put_key_is_open_prevent_mode")
      settings_helper.put_key_is_open_prevent_mode('false')
    end

    if @cleanup_function_runner.should_run?("enable_screen_on_proximity_sensor")
      settings_helper.enable_screen_on_proximity_sensor('0')
    end

    if @device_manufacturer.to_s.downcase.eql?("google")
      settings_helper.ota_disable_automatic_update('1')
      disable_google_fi if @cleanup_function_runner.should_run?("disable_google_fi")
      @adb.shell("settings put system font_scale 1") if @cleanup_function_runner.should_run?("google_font_scale_1")
      restart_sensors if @device_model.eql?("Pixel 6") # old model
    end

    settings_helper.disable_spen if @cleanup_function_runner.should_run?("disable_spen")

    set_screen_density450 if @cleanup_function_runner.should_run?("set_screen_density450")

    if @device_manufacturer.to_s.downcase.eql?("samsung") && @device_type.to_s.downcase.eql?("tablet")
      settings_helper.disable_spen
    end

    case @device_model
    when /SM-T865|SM-T875/ # Tab S6, S7 (old models)
      settings_helper.adaptive_fast_charging('1')

    when /SM-G981B|SM-G980F|SM-G981F|SM-G991B/ # S20, S21 (old models)
      @adb.shell('wm size 1080x2400; wm density 480')
      settings_helper.update_wifi_only2("0")

    when /SM-S901B|SM-S901E/ # S22
      @adb.shell('pm disable-user com.samsung.android.cidmanager')

    when /SM-G986B|SM-G996B/ # S20 Plus, S21 Plus
      @adb.shell('wm size 1080x2400')
      settings_helper.update_wifi_only2("0")

    when /SM-G988B|SM-G998B/ # S20 Ultra, S21 Ultra
      @adb.shell('wm size 1080x2400; wm density 420')
      settings_helper.update_wifi_only2("0")

    when /SM-S918B/ # S23 Ultra #
      @adb.shell('wm size 1080x2316')

    when /SM-G950F|SM-G955F/ # S8, S8 Plus
      @adb.shell('wm size reset;wm density reset')

    when /SM-A115M|SM-A515F/ # A11, A51
      @adb.shell('pm disable-user com.sec.android.app.SecSetupWizard')

    when /IN2011|IN2013|HD1900|HD1901|HD1903|HD1905|HD1907|LE2111|LE2113/ # OnePlus 8, 7T, 9
      settings_helper.screen_off_timeout('86400000')
      settings_helper.wifi_should_switch_network('0')
      root_command.run('pm disable com.oneplus.setupwizard')

    when /SM-G900F|SM-G900H/ # S5
      root_command.run('pm disable com.sec.android.app.samsungapps')

    when 'M2003J15SC' # Redmi Note 9
      @adb.shell('pm disable-user com.mi.globalbrowser')

    when 'SM-A515F' # A51
      @adb.shell('pm disable-user com.dti.samsung')
    end
  end

  def manufacturer_based_updates
    case @adb.manufacturer
    when 'OPPO'
      settings_helper.timepower_config("070010230000080010000000")
    when 'vivo'
      settings_helper.poweron_time("1652770800000")
    when 'OnePlus'
      settings_helper.def_timepower_config("060001000000")
      @adb.shell("settings put system font_scale 1")
      @adb.shell("wm density reset")
    when 'HUAWEI'
      settings_helper.next_timing_boot_timestamp("1652853600000")
    when 'samsung'
      samsung_pass_autofill = 'com.samsung.android.samsungpassautofill'
      disable_autofill = 'settings put secure autofill_service none'

      if @os_version.to_i >= 7
        settings_helper.disable_nearby_device_scanning
        if device_obj.rooted?
          root_command.run("pm disable #{samsung_pass_autofill}; #{disable_autofill}")
        else
          @adb.shell("pm disable-user #{samsung_pass_autofill}; #{disable_autofill}")
        end
      end
    end
  end

  def disable_battery_optimisation
    packages = [
      '+com.android.browserstack',
      '+com.google.android.vpntether',
      '+fr.pchab.AndroidRTC',
      '+io.appium.uiautomator2.server',
      '+io.appium.uiautomator2.server.test',
      '+com.genymobile.gnirehtet',
      '+com.browserstack.reversetether'
    ].join(" ")

    if RTCAppReleaseHelper.new.should_install_rtc_app?('v2', @device_model, @os_version.to_i)
      packages.concat(" +fr.pchab.AndroidRTC2")
      packages.concat(" +com.android.talkback") if @os_version.to_i > 10
    end

    @adb.dumpsys("deviceidle whitelist #{packages}")
  end

  def set_keyboard_back_to_default
    keyboard_val = @adb.shell("ime list -s")

    case keyboard_val
    when %r{com.sec.android.inputmethod/.SamsungKeypad}
      @adb.shell("ime set 'com.sec.android.inputmethod/.SamsungKeypad'")
    when %r{com.samsung.android.honeyboard/.service.HoneyBoardService}
      @adb.shell("ime set 'com.samsung.android.honeyboard/.service.HoneyBoardService'")
    when %r{com.google.android.inputmethod.latin/com.android.inputmethod.latin.LatinIME}
      @adb.shell("ime set 'com.google.android.inputmethod.latin/com.android.inputmethod.latin.LatinIME'")
    end
  end

  def disable_samsung_game_tools
    game_tools_enabled = @adb.list_packages("-e", filter: "com.samsung.android.game.gametools")
    log :debug, "Gametools enabled status - #{game_tools_enabled}"

    unless game_tools_enabled.empty?
      log :info, "Disabling gametools", cleanup_flow: true
      if bsrun_device?(@device_id)
        root_command.run("pm disable com.samsung.android.game.gametools")
      else
        @adb.shell("pm disable-user --user 0 com.samsung.android.game.gametools")
      end
    end

    # Uninstall gametools app for Samsung S7 as pm disable does not work - MOBPE-980
    return unless @device_model.include?("SM-G930F")

    log :debug, "Uninstalling gametools"
    @adb.uninstall("com.samsung.android.game.gametools", '--user', '0')
  end

  def update_settings
    settings_helper.screen_brightness_mode("0")
    settings_helper.screen_brightness("0")
    settings_helper.screen_off_timeout("0")
    settings_helper.screen_off_pocket("0")
    settings_helper.oem_acc_anti_misoperation_screen("0")
    settings_helper.stay_on_while_plugged_in("0")
    settings_helper.wifi_sleep_policy("2")
    settings_helper.aod_mode("0")
    settings_helper.immersive_mode_confirmations("confirmed")
    settings_helper.update_peak_refresh_rate(device_obj.peak_refresh_rate)
    enable_location.run
    clear_setup_wizard
    update_location_package_blacklist
    enable_google_location_services
    settings_helper.disable_notification_channel_warnings
    settings_helper.disable_double_tap_to_sleep
    device_model_based_updates
    manufacturer_based_updates
    @adb.shell("pm disable-user com.android.vending") unless device_obj.full_reset_device?
    settings_helper.hidden_api_policy("1") if @os_version.to_i >= 11
    @adb.shell("svc nfc disable") if @os_version.to_i >= 7
    disable_battery_optimisation if @os_version.to_i > 6
    settings_helper.bluetooth_automatic_turn_on("0") if @os_version.to_i >= 15
    remove_voice_input_method_service if @device_obj.manufacturer == "Google" && @os_version.to_i >= 15
    set_keyboard_back_to_default
    settings_helper.clock_set_24h
    popup_helper.dismiss_location_warning_popup if @device_model.include?("SM-G991B")
    disable_telephony_alert_samsung
    touch_battery_metrics_file
    disable_vibrations
    disable_sounds
    disable_taskbar
    device_obj.toggle_animation(0)
    clear_nexus_launcher
  end

  def clear_setup_wizard
    return unless @device_model.include?("Pixel")

    log :info, "Clearing Setup Wizard For Pixel Device", cleanup_flow: true
    resp = @adb.shell("pm clear com.google.android.setupwizard")
    log :info, "Output of Clearing Setup Wizard : #{resp}"
  end

  def disable_vibrations
    settings_helper.disable_vibrations if device_obj.disable_sound_device?
  end

  def disable_sounds
    settings_helper.disable_sounds if device_obj.disable_sound_device?
  end

  def clear_nexus_launcher
    settings_helper.clear_nexus_launcher if device_obj.clear_nexus_launcher?
  end

  def disable_taskbar
    return unless device_obj.disable_taskbar?

    #For samsung tab s9 and z fold devices disabling taskbar, a feature which is provided in android v13
    log :info, "Disabling taskbar for not getting the suggestion pop up for multi window"
    settings_helper.disable_taskbar
  end

  def log_check_statusbar
    log(:info, 'log_check_statusbar')
    @adb.pull('/data/local/tmp/check_statusbar_log', "/tmp/check_statusbar_log_#{@device_id}")

    output = File.read("/tmp/check_statusbar_log_#{@device_id}")
    log(:info, "check_statusbar output: #{output}")

    @adb.shell('rm -f /data/local/tmp/check_statusbar_log')
  rescue AndroidToolkit::ADB::ExecutionError => e
    return unless e.message.include?('No such file or directory')

    log :info, '/data/local/tmp/check_statusbar_log doesn\'t exist'

    return unless File.exist?("/tmp/duplicate_session_#{@device_id}")

    log :info, 'duplicate session file exists and no check status bar'
    zombie_key_value(
      platform: 'android',
      kind: 'check_statusbar_fail',
      data: @last_session_type,
      device: @device_id,
      os_version: @os_version.to_s,
      session: @session_id
    )
  end

  def ensure_essential_binaries
    log :info, "ensure_essential_binaries"
    essential_binaries.each  do |binary|
      log :info, "checking binary #{binary}"
      bin_file = @adb.ls("/data/local/tmp/#{binary}")
      if bin_file.empty?
        log(:info, "Binary #{binary} doesn't exist, pushing")
        system("sh chmod a+x #{webrtc_dir}/#{binary}")
        @adb.push("#{webrtc_dir}/#{binary}", "/data/local/tmp/")
      else
        log(:info, "Binary #{binary} exists")
      end
    end
  end

  def fix_pid(pid)
    pid.split(/\D+/).join
  end

  def kill_device_binaries
    log :info, "kill_device_binaries"
    adb_cmd = "/data/local/tmp/busybox pgrep -f \"bs_screencap|interactionsserver|interactions_server|uiautomator\""\
              " | /data/local/tmp/busybox xargs kill -2"
    begin
      @adb.shell(adb_cmd)
    rescue AndroidToolkit::ADB::ExecutionError => e
      if e.message.include?("kill: missing argument")
        log(:info, "bs_screencap|interactionsserver|interactions_server|uiautomator not running")
      end
    end

    if device_obj.run_interactions_server_with_bsrun?
      log :info, "interactions_server needs to run with bsrun"
      cmd = "ps -ef | grep -E 'interactionsserver|interactions_server' | grep -v grep | awk '{print $2}'"
      interactions_server_pid_list = @adb.shell(cmd).split("\n")
      interactions_server_pid_string = interactions_server_pid_list.reduce("") do |pid_str, curr_pid|
        pid_str += "#{fix_pid(curr_pid)} "
      end
      root_command.run("kill #{interactions_server_pid_string}")
    end

    adb_cmd = "ps -ef"\
              " | grep io.devicefarmer.minicap.Main"\
              " | grep -v grep"\
              " | /data/local/tmp/busybox awk '{print $2}'"\
              " | /data/local/tmp/busybox  xargs kill"
    begin
      @adb.shell(adb_cmd)
    rescue AndroidToolkit::ADB::ExecutionError => e
      log :info, "minicap not running" if e.message.include?("kill: missing argument")
    end
    @adb.shell("rm -f /data/local/tmp/check_statusbar")
  end

  def stop_device_logger_monitoring
    log :info, "Stopping device logger metric monitoring"
    DeviceLoggerMetric.new(@device_id, @session_id, @logger).stop_monitoring
  end

  def oppo_usb_security_payment_issue_devices?(oppo_model)
    [
      "CPH2333", #Oppo A96 - 11.0
      "CPH2251", #Oppo Reno 6, 11.0
      "CPH2035",  #Oppo Reno 3 Pro - 10.0
      "RMX3085" #Realme 8 - 11.0
    ].include?(oppo_model)
  end

  def handle_security_alerts
    # security alert handling for huawei-p30 devices
    if ["ELE-L04", "ELE-L09", "ELE-L29"].include?(@device_model)
      log :info, "Handle security alert for huawei #{@device_model}", cleanup_flow: true
      @adb.shell("appops set com.huawei.systemmanager android:system_alert_window deny")
    elsif oppo_usb_security_payment_issue_devices?(@device_model)
      #Disable security popup when payment apps are opened
      #with usb data transfer mode for OPPO devices
      disable_user_resp = @adb.shell("pm disable-user --user 0 com.coloros.securepay")
      log :info, "Disabling usb charging popup #{@device_model}, Response : #{disable_user_resp}", cleanup_flow: true
    end
  end

  def stop_mock_location_service
    # Killing both as using driver.set_location will run the appium's location service
    # even without passing the location capability
    log :info, "Stopping mock location service", cleanup_flow: true
    begin
      @adb.shell("am stopservice --user 0 -n io.appium.settings/.LocationService")
    rescue AndroidToolkit::ADB::ExecutionError => e
      log :error, "Error in executing adb command: #{e.message}"
    end
    begin
      @adb.shell("am stopservice --user 0 -n com.android.browserstack/.services.LocationMockService")
    rescue AndroidToolkit::ADB::ExecutionError => e
      log :error, "Error in executing adb command: #{e.message}"
    end
    settings_helper.restart_location
    @adb.shell("appops set io.appium.settings android:mock_location deny")
  end

  def check_outside_browser
    BrowserStackUtils::LogCleanupFlow.log(@device_id, Time.now.to_i, "Checking outside browser")
    temp_outside_browser_file = "/data/local/tmp/outside_browser"
    outside_browser_file = "/tmp/#{@device_id}_outside_browser"
    return if @adb.ls(temp_outside_browser_file).empty?

    @adb.pull(temp_outside_browser_file, outside_browser_file)
    session_params = begin
      JSON.parse(File.read("/tmp/duplicate_session_#{@device_id}"), symbolize_names: true)
    rescue StandardError
      {}
    end
    push_to_cls(session_params, "live_session_report", "", { "went_outside_browser" => "true" })
    @adb.shell("rm #{temp_outside_browser_file}")
  end

  def handle_samsung_recommendation_app_popup
    # popup alert handling for Samsung A52 devices (MOBPE-1013)
    return unless ["SM-A525F"].include?(@device_model)

    log :info, "Handle Recommendation App for A52 #{@device_model}", cleanup_flow: true
    @adb.shell("appops set com.samsung.android.app.omcagent android:system_alert_window deny")
  end

  def reduce_volume
    log :info, "reduce_volume"
    service_params = JSON.parse(File.read("./android/service_params.json"))
    stream_volume = service_params['setStreamVolume']["v#{@os_version.to_i}"] || '7'
    1.upto(7) do |i|
      log :info, "reducing volume #{i}, #{stream_volume}"
      @adb.shell("service call audio #{stream_volume} i32 #{i} i32 100 i32 1")
    end
  end

  def check_scrcpy_detected
    log :info, "check_scrcpy_detected"
    scrcpy_file = @adb.ls("/data/local/tmp/scrcpy_detected")
    if scrcpy_file.empty?
      log :info, "No screen copy"
      return
    end

    log :info, "scrcpy detected"
    zombie_key_value(
      platform: 'android',
      kind: "scrcpy-detected",
      data: @last_session_type,
      device: @device_id,
      os_version: @os_version.to_s,
      session: @session_id
    )
    @adb.shell("rm -f /data/local/tmp/scrcpy_detected")
  end

  def reset_custom_props
    # this function is used to reset any user specific props that might be set for session to blank string
    # props might be set in https://github.com/browserstack/mobile/blob/60e06857d5da5ee1ac5e28ee5a3c54924bb80853/android/server.rb#L1993-L2013
    log :info, "reset_custom_props from ruby", cleanup_flow: true
    all_props = @adb.getprop.scan(/\[(.*)\]:/).flatten
    all_props.each do |key|
      next unless key.include?(CUSTOM_PROP) || key.include?(MAESTRO_PROP)

      log :info, "  resetting prop #{key}", cleanup_flow: true
      @adb.shell('setprop', key, '""')
    end
  end

  def enable_homescreen_rotation
    return if @os_version.to_i < 10

    begin
      log :info, "Enabling homescreen rotation" # MOBPE-1063
      if @device_model.include?("Pixel") && device_obj.rooted?
        launcher_file = "com.android.launcher3.xml"
        launcher_file_path = "/usr/local/.browserstack/mobile/android/helpers/launcher_preferences/#{launcher_file}"
        path_on_device = "/data/data/com.google.android.apps.nexuslauncher/shared_prefs/com.android.launcher3.prefs.xml"
        @adb.push(launcher_file_path, "/sdcard/")
        root_command.run("cp /sdcard/#{launcher_file} #{path_on_device}")
        root_command.run("am restart com.android.launcher3")
      elsif @device_model.include?("Pixel")
        ui_automation_helper.allow_home_screen_rotation_enabled
      elsif @device_model.include?("SM-")
        @adb.shell("settings put global sehome_portrait_mode_only 0")
      end
    rescue StandardError => e
      log :error, "Unable to enable homescreen rotation: #{e.message}"
      zombie_key_value(
        platform: 'android',
        kind: 'homescreen_rotation_failure',
        data: @last_session_type,
        device: @device_id,
        os_version: @os_version.to_s,
        session: @session_id
      )
    end
  end

  def disable_telephony_alert_samsung
    # mobile-plan and esim popup alert handling for Samsung S21 devices (MOBPE-1020)
    return unless @device_model.include?("SM-G99")

    log :info, "Handle Popup for S21 device #{@device_model}", cleanup_flow: true
    @adb.shell("appops set com.samsung.android.app.telephonyui android:system_alert_window deny")
  end

  def push_user_installed_applist_to_cls
    log :info, "Pushing installed applist to cls from ruby", cleanup_flow: true
    session_params = begin
      JSON.parse(File.read(DUPLICATE_SESSION_FILE.gsub('device_id', @device_id)), symbolize_names: true)
    rescue StandardError
      {}
    end
    return if session_params[:genre] != 'app_live_testing'

    all_apps = @adb.list_packages("-u -i -3")
    play_store_apps = []
    uploaded_apps = []
    updated_apps = []

    all_apps.each do |app|
      package, installer = app.split
      play_store_apps += [package.split(":")[1]] if installer.include?('com.android.vending')
      uploaded_apps += [package] if installer.include?('null')
    end

    eds = EDS.new({}, @logger)
    if !play_store_apps.empty? && File.exist?(PLAYSTORE_APPS.gsub('device_id', @device_id))
      play_store_apps -= File.read(PLAYSTORE_APPS.gsub('device_id', @device_id)).split("\n")
      all_installed_apps = File.read(ALL_SYSTEM_AND_UPLOADED_APPS.gsub('device_id',
                                                                       @device_id)).split("\n")

      all_installed_apps.each do |app|
        updated_apps += [app] if play_store_apps.include? app
      end
      play_store_apps -= updated_apps
      play_store_apps -= KNOWN_PLAYSTORE_INSTALLED_APPS
      unless play_store_apps.empty?
        push_to_cls(session_params, 'store_apps', '', { "store_apps" => play_store_apps.join(',') })
        eds.push_logs('app_live_test_sessions',
                      { genre: 'app_live_testing', session_id: @session_id,
                        product: { store_app_count: play_store_apps.join(' ') } })
      end

      if session_params[:app_hashed_id] == 'play_store'
        eds.push_logs('app_live_web_events',
                      { genre: 'app_live_testing', session_id: @session_id,
                        store_app_count: play_store_apps.join(' '),
                        event_name: "play_store_session" })
      end

      unless updated_apps.empty?
        push_to_cls(session_params, 'updated_apps', '', { "updated_apps" => updated_apps.join(',') })
      end
    end

    if !uploaded_apps.empty? && File.exist?(UPLOADED_APPS.gsub('device_id', @device_id))
      uploaded_apps -= File.read(UPLOADED_APPS.gsub('device_id', @device_id)).split("\n")

      unless uploaded_apps.empty?
        push_to_cls(session_params, 'uploaded_apps', '', { "uploaded_apps" => uploaded_apps.join(',') })
        eds.push_logs('app_live_test_sessions',
                      { genre: 'app_live_testing', session_id: @session_id,
                        product: { uploaded_app_count: uploaded_apps.join(' ') } })
      end
    end
  end

  def handle_reboot_in_cleanup
    needs_reboot_file = StateFileHelper.new("needs_reboot_#{@device_id}")
    install_failures_file_name = "needs_reboot_for_install_failures_#{@device_id}"
    needs_reboot_for_install_failures = StateFileHelper.new(install_failures_file_name)
    unless device_obj.reboot_after_session? || needs_reboot_file.exist? || needs_reboot_for_install_failures.exist?
      return
    end

    log :info, "Rebooting device to enhance stability", cleanup_flow: true
    device_obj.reboot_and_wait('enhance_stability_reboot')

    needs_reboot_file.remove
    needs_reboot_for_install_failures.remove
  end

  def disable_google_fi
    @adb.shell('pm disable-user --user 0 com.google.android.apps.tycho')
  end

  def check_battery
    log :info, "check_battery from ruby", cleanup_flow: true
    battery_level = device_obj.battery_level
    if battery_level < 25
      msg = "Device battery too low: #{battery_level}. Stopping cleanup and switching screen off till charged"
      log :info, msg, cleanup_flow: true
      device_obj.reboot_and_wait('low_battery_reboot')
      update_settings
      FileUtils.touch("/tmp/unclean_low_battery_#{@device_id}")
      raise 'Low battery'
    end
  rescue BatteryLevelNotFound => e
    log :error, "Failed to get battery level, #{e.message}"
    battery_error_state_file = StateFileHelper.new("battery_error_#{@device_id}")
    if battery_error_state_file.older_than_days?(1)
      # log to zombie if state file does not exist or state file's time is greater than a day
      battery_error_state_file.reset
      zombie_key_value(
        platform: 'android',
        kind: 'fetch_battery_status_fail',
        data: @last_session_type,
        device: @device_id,
        os_version: @os_version.to_s,
        session: @session_id
      )
    end
  end

  # store media projection token in cleanup and use that in the next session to reduce connect time
  # we are not keeping media projection enabled for all times, we are just storing the token
  # the token is consumed in the next session when streaming is started
  # this will improve connect time for products using media projection to run sessions
  # as of now Live, App Live, Automate and App Automate use media projection
  # note: this method is being called behind a LD flag from railsApp `lse_android_mp_in_cleanup`
  def store_media_projection_token
    # not doing this for 64-bit devices for now
    return if device_obj.uses_64bit_rtc_app?

    start_time = Time.now

    log :info, "[PRE_START] call pre-start action"
    @adb.am("startservice --user 0 -n fr.pchab.AndroidRTC2/.RTCService --es action pre-start")
    sleep 1

    log :info, "[PRE_START] handle media projection permission"
    `sh #{LIVE_SCRIPT} handle_media_projection_permission #{@device_id}`

    time_taken = Time.now - start_time
    log :info, "[PRE_START] store_media_projection_token took #{time_taken} s"
  end

  def local_testing_chrome_extension_cleanup
    helper = LocalTestingChromeExtension.new(@device_id, @session_id)
    helper.cleanup
  end

  # Disable screen share protection for Android 15
  # More info here: https://browserstack.atlassian.net/browse/MOBPL-5337
  def disable_screen_share_protections
    kill_switch_state_file = StateFileHelper.new("restrict_disable_screen_share_protections_#{@device_id}")
    if @os_version.to_i >= 15 && !kill_switch_state_file.exist?
      log :info, "Disabling screen share protection for Android 15", cleanup_flow: true
      @adb.shell("settings put global disable_screen_share_protections_for_apps_and_notifications 1")
    end
  end

  def remove_observability_sdk_files

    @adb.shell("rm -rf #{TEMP_TEST_OBSERVABILITY_FILE}")
    @adb.shell("rm -rf #{TEMP_SDK_LOG_FILE}")
  rescue StandardError => e
    log "Failed to remove observability and SDK files: #{e.message}"

  end

  def clean_session_video_files
    log :info, "Cleaning session video files"
    @adb.shell("rm -rf /sdcard/screenrecord")
    @adb.shell("rm -rf /sdcard/screenrecord_count")
    FileUtils.rm_rf("/usr/local/.browserstack/video_#{@device_id}")
  rescue StandardError => e
    log :warn, "Error in cleaning session video files: #{e.message}", cleanup_flow: false
  end

  def clean_app_a11y_session_files
    log :info, "Cleaning clean_app_a11y_session_files"
    path = "/data/user/0/com.browserstack.watcher/files"
    session_hash_file = "appa11y_session_hash.json"
    watcher_package_name = "com.browserstack.watcher"
    ls_dir = @adb.shell("run-as #{watcher_package_name} ls #{path}").split("\n")
    @adb.shell("run-as #{watcher_package_name} rm #{path}/#{session_hash_file}") if ls_dir.include?(session_hash_file)
  rescue StandardError => e
    log :warn, "Error in cleaning app a11y session files: #{e.message}", cleanup_flow: true
  end

  def clean_watcher_google_block_file
    log :info, "Cleaning clean_watcher_google_block_file"
    file_name = "googlesignup_block.txt"
    watcher_package_name = "com.browserstack.watcher"
    path = "/data/user/0/#{watcher_package_name}/files"
    ls_dir = @adb.shell("run-as #{watcher_package_name} ls #{path}").split("\n")
    @adb.shell("run-as #{watcher_package_name} rm #{path}/#{file_name}") if ls_dir.include?(file_name)
    log :info, "Cleaning clean_watcher_google_block_file done"
  rescue StandardError => e
    log :warn, "Error in clean_watcher_google_block_file: #{e.message}", cleanup_flow: true
  end

  def remove_voice_input_method_service
    log :info, "Removing voice input method service and re-adding Google Input Method service"
    voice_service = "com.google.android.tts/com.google.android.apps.speech.tts.googletts.settings.asr.voiceime"\
              ".VoiceInputMethodService"
    latin_service = "com.google.android.inputmethod.latin/com.android.inputmethod.latin.LatinIME"
    @adb.shell("ime disable #{voice_service}")
    @adb.shell("ime enable #{latin_service}")
  end

  def enable_location_accuracy_rooted
    # Hacky way to enable Google Location Accuracy on Rooted devices :)
    @adb.push("#{PATH}/helpers/google_location_settings/googlesettings.db", "/sdcard/googlesettings.db")
    root_command.run("cp /sdcard/googlesettings.db /data/data/com.google.android.gsf/databases/googlesettings.db")
  rescue StandardError => e
    BrowserStack.logger.error("Error in pushing googlesettings db: #{e.message}")
    zombie_key_value(
      platform: 'android',
      kind: 'google-db-push-failure',
      error: e.message,
      device: @device_id,
      os_version: @os_version.to_s,
      session: @session_id
    )
  end

  private

  def session_type
    File.read("/tmp/sessionis_#{@device_id}").strip
  rescue StandardError
    ""
  end

  def essential_binaries
    ["busybox"]
  end

  def webrtc_dir
    "/usr/local/.browserstack/webrtc"
  end

  def remove_file(path)
    if Dir.exist?(path)
      Dir["#{path}/*"].each do |file_path|
        remove_file(file_path) if Dir.exist?(file_path)
        File.delete(file_path)
      end
      Dir.delete(path)
    end
    File.delete(path) if File.exist?(path)
  end

  # FIXME: switch to ruby approach.
  def mobile_session_get(key)
    cmd = "sh #{BS_DIR}/mobile-common/mobile_session_info/lib/common-session.sh "\
          "mobile_session_get #{@device_id} #{key} | tr -d '\"'"
    `#{cmd}`
  end

  def log(level, msg, cleanup_flow: false)
    BrowserStackUtils::LogCleanupFlow.log(@device_id, Time.now.to_i, msg) if cleanup_flow
    if @logger.instance_of?(Logger)
      formatted_msg = "#{self.class} #{msg}"
      @logger.send(level.to_sym, formatted_msg)
    else
      @logger.send(level.to_sym, msg, @logger_params)
    end
  end
end

if $PROGRAM_NAME == __FILE__
  begin
    command = ARGV[0].to_s.strip.downcase.to_sym
    device_id = ARGV[1].to_s.strip
    session_id = ARGV[2].to_s.strip
    last_session_type = ARGV[3].to_s.strip
    helper = MainCleanup.new(device_id, session_id, Logger.new($stdout), last_session_type)
    if ARGV[4]
      helper.send(command, *ARGV[4..])
    else
      helper.send(command)
    end
  rescue StandardError => e
    ExitFile.write(e.message)
    raise e
  end
end
