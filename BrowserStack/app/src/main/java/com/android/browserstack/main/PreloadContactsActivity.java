package com.android.browserstack.main;

import android.annotation.SuppressLint;
import android.content.ContentResolver;
import android.database.Cursor;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import android.provider.ContactsContract;
import android.util.Log;
import android.content.Intent;
import android.os.Build;

import android.app.Activity;

import com.android.browserstack.helpers.ContactsHelper;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.ArrayList;
import java.util.Arrays;

import ezvcard.Ezvcard;
import ezvcard.VCard;
import ezvcard.property.Email;
import ezvcard.property.StructuredName;
import ezvcard.property.FormattedName;
import ezvcard.property.Telephone;

public class PreloadContactsActivity extends Activity {

    public static final String TAG = "PreloadContactsActivity";
    private static final String pathToPreloadedFile = Environment.getExternalStorageDirectory().getAbsolutePath() + "/contact_count";
    public static final String CONTACT = "contact";
    public static final String CONTACT_BASE_PATH = "/sdcard/Download/";
    public static final String SESSION_ID = "session_id";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        Intent intent = this.getIntent();
        boolean cleanPreviousContacts = true;
        if(intent.hasExtra("clean")) cleanPreviousContacts = Boolean.parseBoolean(intent.getStringExtra("clean"));
        // deleting previous contacts
        ContentResolver contentResolver = getApplicationContext().getContentResolver();
        Cursor cursor = contentResolver.query(ContactsContract.Contacts.CONTENT_URI, null, null, null, null);
        if(cleanPreviousContacts){
            Log.i(TAG, "Trying to clean any previous contacts");
            if (cursor != null) {
                while (cursor.moveToNext()) {
                    String lookupKey = cursor.getString(cursor.getColumnIndex(ContactsContract.Contacts.LOOKUP_KEY));
                    Uri uri = Uri.withAppendedPath(ContactsContract.Contacts.CONTENT_LOOKUP_URI, lookupKey);
                    contentResolver.delete(uri, null, null);
                }
                cursor.close();
            } else {
                Log.d(TAG, "No contacts found to delete");
            }
        }

        String action = "";
        String session_id = "";
        String[] contacts = null;
        List<File> files = new ArrayList<File>();
        if (intent.hasExtra("action")) action = intent.getStringExtra("action");

        if ("insert".equalsIgnoreCase(action)){
            if (intent.hasExtra(SESSION_ID)) {
                session_id = intent.getStringExtra(SESSION_ID);
                Log.i(TAG, "Invoked for session id: " + session_id);
            } else {
                Log.i(TAG, "Session id not provided ");
            }

            if (intent.hasExtra(CONTACT)) {
                contacts = intent.getStringArrayExtra(CONTACT);
                // The escape character '\' does not get removed by getStringArrayExtra
                for (int i = 0; i < contacts.length; i++) {
                    contacts[i] = contacts[i].replace("\\", "");
                    File file = new File(CONTACT_BASE_PATH, contacts[i]);
                    files.add(file);
                }
                Log.i(TAG, "[" + session_id + "] " + "Attempting to operate on contacts: " + Arrays.toString(contacts));
            }
        }
        if(files.isEmpty()){
            files.add(new File(Environment.getExternalStorageDirectory().getAbsolutePath(), "contacts_import.vcf"));
        }

        // inserting vcf to contacts
        List<VCard> list = new ArrayList<VCard>();
        for(File file : files){
            try {
                list.addAll(Ezvcard.parse(file).all());
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        boolean status = true;
        if (list != null && !list.isEmpty()) {
            for (VCard vcard : list) {
                StructuredName sn = vcard.getStructuredName();
                FormattedName fn = vcard.getFormattedName();
                String displayName = "", firstName = null, lastName = "", emailId = "", telephoneNo = "";
                if (sn != null) {
                    firstName = sn.getGiven();
                    lastName = sn.getFamily();
                    if (firstName != null && lastName != null) {
                        displayName = firstName + " " + lastName;
                    } else if (firstName != null) {
                        displayName = firstName;
                    } else {
                        Log.d(TAG, "Provided N field is incorrect. At-least pass First name in N field.");
                    }
                }
                else if (fn != null) {
                    if (fn.getValue() != null) {
                        displayName = fn.getValue();
                    } else {
                        Log.d(TAG, "Provided FN field is incorrect. At-least pass some string in FN field.");
                    }
                } else {
                    Log.d(TAG, "Name is empty, Please provide Contact Card with FN or N field.");
                }

                List<Email> emails = vcard.getEmails();
                emailId = emails.isEmpty() ? emailId : emails.get(0).getValue();
                if (emails.isEmpty()) {
                    Log.d(TAG, "Email is empty");
                }
                List<Telephone> telephones = vcard.getTelephoneNumbers();
                telephoneNo = telephones.isEmpty() ? telephoneNo : telephones.get(0).getText();
                if (telephones.isEmpty()) {
                    Log.d(TAG, "Telephone is empty");
                }
                status &= ContactsHelper.insertContact(getContentResolver(), displayName, lastName, emailId, telephoneNo);
            }
            Log.d(TAG, "vcard pushed to Contact App. status: " + status);
        } else {
            Log.e(TAG, "No vcard found on the device");
            finish();
        }

        // getting count of contacts
        cursor = managedQuery(ContactsContract.CommonDataKinds.Phone.CONTENT_URI, null, null, null, null);
        int count = cursor.getCount();
        cursor.close();
        Log.d(TAG, "Total contacts added: " + count);

        // writing count to a file
        File file_to_write_count = new File(pathToPreloadedFile);
        FileOutputStream stream = null;
        try {
            if(file_to_write_count.createNewFile()){
                Log.d(TAG, "Created: /sdcard/contact_count");
            }
            stream = new FileOutputStream(file_to_write_count);
            stream.write(Integer.toString(count).getBytes());
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if(stream != null) {
                    stream.close();
                    Log.d(TAG, "Count written to file:" + count);
                } else {
                    Log.w(TAG, "Unable to write count in the specified file. OutputStream instance is null");
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
            finish();
        }


    }
}