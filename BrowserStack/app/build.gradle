plugins {
    id 'com.android.application'
}

android {
    signingConfigs {
        debug {
            storeFile file('./BrowserStack')
            keyAlias 'BrowserStack'
            storePassword 'c0stac0ff33'
            keyPassword 'c0stac0ff33'
        }
    }
    compileSdk 28
    useLibrary 'org.apache.http.legacy'
    defaultConfig {
        applicationId "com.android.browserstack"
        minSdk 15
        targetSdk 28
        versionCode 48
        versionName "48.0"
        multiDexEnabled true

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation 'org.lsposed.hiddenapibypass:hiddenapibypass:4.3'
    implementation 'androidx.appcompat:appcompat:1.2.0'
    implementation 'com.google.android.material:material:1.1.0-alpha09'
    //implementation 'com.google.android.material:material:1.6.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation files('libs/android-support-v4.jar')
    implementation files('libs/AndroidProxyLibrary.jar')
    implementation files('libs/aws-android-sdk-1.7.1.1.jar')
    implementation files('libs/ez-vcard-0.10.6.jar')
//    implementation files('libs/rootbeer-lib-0.0.6.jar')
     //implementation files('libs/rootbeer-lib-0.0.6.jar')
//    implementation 'com.scottyab:rootbeer-lib:0.0.6'
    implementation 'com.scottyab:rootbeer-lib:0.1.0'  // Can be used to remove the dependency
    //implementation files('libs/rootbeer-lib-0.1.0.jar')
    implementation files('libs/timber.jar')
    implementation files('libs/vinnie-2.0.2.jar')
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'
}
